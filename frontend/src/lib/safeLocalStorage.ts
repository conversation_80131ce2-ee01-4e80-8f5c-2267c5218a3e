/**
 * Safe localStorage utility that handles SSR and browser compatibility
 * This prevents errors when localStorage is not available (e.g., during SSR)
 */
export const safeLocalStorage = {
  /**
   * Safely get an item from localStorage
   * @param key - The key to retrieve
   * @returns The value or null if not found/not available
   */
  getItem: (key: string): string | null => {
    if (typeof window !== 'undefined' && window.localStorage) {
      try {
        return localStorage.getItem(key);
      } catch (error) {
        console.warn(`Failed to get localStorage item "${key}":`, error);
        return null;
      }
    }
    return null;
  },

  /**
   * Safely set an item in localStorage
   * @param key - The key to set
   * @param value - The value to store
   */
  setItem: (key: string, value: string): void => {
    if (typeof window !== 'undefined' && window.localStorage) {
      try {
        localStorage.setItem(key, value);
      } catch (error) {
        console.warn(`Failed to set localStorage item "${key}":`, error);
      }
    }
  },

  /**
   * Safely remove an item from localStorage
   * @param key - The key to remove
   */
  removeItem: (key: string): void => {
    if (typeof window !== 'undefined' && window.localStorage) {
      try {
        localStorage.removeItem(key);
      } catch (error) {
        console.warn(`Failed to remove localStorage item "${key}":`, error);
      }
    }
  },

  /**
   * Safely clear all localStorage items
   */
  clear: (): void => {
    if (typeof window !== 'undefined' && window.localStorage) {
      try {
        localStorage.clear();
      } catch (error) {
        console.warn('Failed to clear localStorage:', error);
      }
    }
  }
};
