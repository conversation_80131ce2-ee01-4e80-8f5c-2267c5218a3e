import { Project, FileUploadResponse, SheetPreview, ImportRequest, ImportResponse, ColumnMapping } from '@/types';

interface User {
  id: number;
  email: string;
  full_name?: string;
  is_active: boolean;
  created_at: string;
  updated_at?: string;
}

interface AuthResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
}

interface ProjectListItem {
  id: number;
  name: string;
  slug: string;
  description?: string;
  created_at: string;
  member_count: number;
  requirement_count: number;
  test_case_count: number;
  user_role: 'owner' | 'member';
}

interface Requirement {
  id: number;
  name: string;
  slug: string;
  description: string;
  refined_description?: string;
  project_id: number;
  created_by: number;
  created_at: string;
  updated_at?: string;
  tags: Tag[];
  test_cases: TestCase[];
}

interface RequirementListItem {
  id: number;
  name: string;
  slug: string;
  created_at: string;
  created_by: number;
  created_by_name?: string;
  tags: Tag[];
  test_case_count: number;
}

interface TestCase {
  id: number;
  custom_id: string;
  title: string;
  steps?: string;
  expected_result?: string;
  notes?: string;
  requirement_id: number;
  created_at: string;
  updated_at?: string;
}

interface Tag {
  id: number;
  name: string;
  color?: string;
  created_at: string;
}

interface ProjectVariable {
  id: number;
  project_id: number;
  key: string;
  value: string;
  description?: string;
  created_at: string;
  updated_at?: string;
}

interface ProjectMember {
  id: number;
  user_id: number;
  user_email: string;
  user_name?: string;
  role: 'owner' | 'member';
  joined_at: string;
}

interface UserSearchResult {
  id: number;
  email: string;
  full_name?: string;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';

class ApiClient {
  private readonly baseURL: string;
  private onUnauthorized?: () => void;
  private refreshPromise?: Promise<boolean>;
  private lastActivityTime: number = Date.now();
  private activityCheckInterval?: NodeJS.Timeout;
  private isLoggedOut: boolean = false;
  private activityListeners: Array<{ event: string; handler: () => void }> = [];
  private visibilityChangeHandler?: () => void;

  // Helper method to safely access localStorage (SSR-safe)
  private safeLocalStorage = {
    getItem: (key: string): string | null => {
      if (typeof window !== 'undefined' && window.localStorage) {
        return localStorage.getItem(key);
      }
      return null;
    },
    setItem: (key: string, value: string): void => {
      if (typeof window !== 'undefined' && window.localStorage) {
        localStorage.setItem(key, value);
      }
    },
    removeItem: (key: string): void => {
      if (typeof window !== 'undefined' && window.localStorage) {
        localStorage.removeItem(key);
      }
    }
  };

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    this.isLoggedOut = false; // Reset logged out state on new instance
    this.setupActivityTracking();
  }

  setUnauthorizedHandler(handler: () => void) {
    this.onUnauthorized = handler;
  }

  private cleanupActivityTracking() {
    // Remove existing event listeners
    if (typeof window !== 'undefined') {
      this.activityListeners.forEach(({ event, handler }) => {
        if (event === 'focus') {
          window.removeEventListener(event, handler);
        } else {
          document.removeEventListener(event, handler, true);
        }
      });

      if (this.visibilityChangeHandler) {
        document.removeEventListener('visibilitychange', this.visibilityChangeHandler);
        this.visibilityChangeHandler = undefined;
      }
    }

    this.activityListeners = [];

    // Clear interval
    if (this.activityCheckInterval) {
      clearInterval(this.activityCheckInterval);
      this.activityCheckInterval = undefined;
    }
  }

  private setupActivityTracking() {
    // Clean up any existing tracking first
    this.cleanupActivityTracking();

    // Track user activity events
    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];

    const updateActivity = () => {
      // Only update if not logged out
      if (!this.isLoggedOut) {
        this.lastActivityTime = Date.now();
      }
    };

    // Add event listeners for user activity
    if (typeof window !== 'undefined') {
      activityEvents.forEach(event => {
        const handler = updateActivity;
        document.addEventListener(event, handler, true);
        this.activityListeners.push({ event, handler });
      });

      // Handle page visibility changes (user switching tabs, minimizing browser)
      this.visibilityChangeHandler = () => {
        if (document.visibilityState === 'visible' && !this.isLoggedOut) {
          // User came back to the tab, update activity time
          this.lastActivityTime = Date.now();
        }
      };
      document.addEventListener('visibilitychange', this.visibilityChangeHandler);

      // Handle browser sleep/wake scenarios
      const handleFocus = () => {
        if (!this.isLoggedOut) {
          // Check if too much time has passed while browser was inactive
          const now = Date.now();
          const timeSinceActivity = now - this.lastActivityTime;
          const oneHour = 60 * 60 * 1000;

          if (timeSinceActivity > oneHour) {
            console.debug('Browser was inactive for too long, forcing logout');
            this.performIdleLogout();
          } else {
            this.lastActivityTime = now;
          }
        }
      };

      window.addEventListener('focus', handleFocus);
      this.activityListeners.push({ event: 'focus', handler: handleFocus });
    }

    // Check for activity-based token refresh every 5 minutes
    this.activityCheckInterval = setInterval(() => {
      this.checkActivityBasedRefresh();
    }, 5 * 60 * 1000); // 5 minutes
  }

  private checkActivityBasedRefresh() {
    // If already logged out, don't check again
    if (this.isLoggedOut) {
      return;
    }

    const now = Date.now();
    const timeSinceActivity = now - this.lastActivityTime;
    const oneHour = 60 * 60 * 1000; // 1 hour in milliseconds
    const fiveMinutes = 5 * 60 * 1000;

    // Handle edge case: if lastActivityTime is in the future (clock changes)
    if (timeSinceActivity < 0) {
      console.debug('Clock change detected, resetting activity time');
      this.lastActivityTime = now;
      return;
    }

    // If user has been idle for more than 1 hour, force logout
    if (timeSinceActivity > oneHour) {
      console.debug(`User has been idle for ${Math.round(timeSinceActivity / (60 * 1000))} minutes, forcing logout`);
      this.performIdleLogout();
      return;
    }

    // If user was active in the last 5 minutes, check if token needs refresh
    if (timeSinceActivity < fiveMinutes) {
      const tokenInfo = this.getTokenInfo();
      if (tokenInfo && tokenInfo.needsRefresh && !tokenInfo.isExpired) {
        console.debug('User is active and token needs refresh, refreshing proactively');
        this.refreshToken().catch(error => {
          console.error('Proactive token refresh failed:', error);
        });
      }
    }
  }

  private getTokenInfo() {
    const token = this.safeLocalStorage.getItem('token');
    if (!token) return null;

    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const currentTime = Math.floor(Date.now() / 1000);
      const timeUntilExpiry = payload.exp - currentTime;

      return {
        token,
        payload,
        currentTime,
        timeUntilExpiry,
        isExpired: payload.exp < currentTime,
        needsRefresh: timeUntilExpiry < 15 * 60 // Refresh if less than 15 minutes remaining
      };
    } catch (error) {
      console.error('Error parsing token:', error);
      return null;
    }
  }

  private async refreshToken(): Promise<boolean> {
    // Prevent multiple simultaneous refresh attempts
    if (this.refreshPromise) {
      return this.refreshPromise;
    }

    this.refreshPromise = this.performTokenRefresh();
    const result = await this.refreshPromise;
    this.refreshPromise = undefined;
    return result;
  }

  private async performTokenRefresh(retryCount: number = 0): Promise<boolean> {
    const refreshToken = this.safeLocalStorage.getItem('refresh_token');
    if (!refreshToken) {
      console.debug('No refresh token available');
      return false;
    }

    const maxRetries = 3;
    const baseDelay = 1000; // 1 second

    try {
      console.debug(`Attempting to refresh access token (attempt ${retryCount + 1}/${maxRetries + 1})`);
      const response = await fetch(`${this.baseURL}/auth/refresh`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ refresh_token: refreshToken }),
      });

      if (!response.ok) {
        console.error('Token refresh failed:', response.status, response.statusText);

        // If it's a 401, the refresh token is invalid - don't retry
        if (response.status === 401) {
          console.debug('Refresh token is invalid, clearing tokens');
          this.safeLocalStorage.removeItem('token');
          this.safeLocalStorage.removeItem('refresh_token');
          return false;
        }

        // For other errors, retry with exponential backoff
        if (retryCount < maxRetries) {
          const delay = baseDelay * Math.pow(2, retryCount);
          console.debug(`Retrying token refresh in ${delay}ms`);
          await new Promise(resolve => setTimeout(resolve, delay));
          return this.performTokenRefresh(retryCount + 1);
        }

        // Max retries reached
        console.error('Max token refresh retries reached, clearing tokens');
        this.safeLocalStorage.removeItem('token');
        this.safeLocalStorage.removeItem('refresh_token');
        return false;
      }

      const data = await response.json();

      // Store new tokens
      this.safeLocalStorage.setItem('token', data.access_token);
      this.safeLocalStorage.setItem('refresh_token', data.refresh_token);

      console.debug('Token refreshed successfully');
      return true;
    } catch (error) {
      console.error('Error refreshing token:', error);

      // Retry on network errors
      if (retryCount < maxRetries) {
        const delay = baseDelay * Math.pow(2, retryCount);
        console.debug(`Retrying token refresh in ${delay}ms due to network error`);
        await new Promise(resolve => setTimeout(resolve, delay));
        return this.performTokenRefresh(retryCount + 1);
      }

      // Max retries reached
      console.error('Max token refresh retries reached due to network errors, clearing tokens');
      this.safeLocalStorage.removeItem('token');
      this.safeLocalStorage.removeItem('refresh_token');
      return false;
    }
  }



  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
    skipAuthRedirect: boolean = false,
    retryCount: number = 0
  ): Promise<T> {
    // Check for idle timeout before making any request
    if (!skipAuthRedirect && !this.isLoggedOut) {
      const now = Date.now();
      const timeSinceActivity = now - this.lastActivityTime;
      const oneHour = 60 * 60 * 1000; // 1 hour in milliseconds

      // Handle edge case: if lastActivityTime is in the future (clock changes)
      if (timeSinceActivity < 0) {
        console.debug('Clock change detected during request, resetting activity time');
        this.lastActivityTime = now;
      } else if (timeSinceActivity > oneHour) {
        console.debug(`User has been idle for ${Math.round(timeSinceActivity / (60 * 1000))} minutes, forcing logout on request`);
        this.performIdleLogout();
        throw new Error('Session expired due to inactivity');
      }
    }

    // Track API activity (only if not being logged out)
    this.lastActivityTime = Date.now();

    // Check if token needs refresh before making request
    if (!skipAuthRedirect && retryCount === 0) {
      const tokenInfo = this.getTokenInfo();
      if (tokenInfo && tokenInfo.needsRefresh && !tokenInfo.isExpired) {
        console.debug('Token needs refresh, refreshing before request');
        await this.refreshToken();
      }
    }

    const url = `${this.baseURL}${endpoint}`;
    const token = this.safeLocalStorage.getItem('token');

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers,
      },
      ...options,
    };

    console.debug('API Request:', { url, method: config.method || 'GET', headers: config.headers });

    try {
      const response = await fetch(url, config);

      console.debug('API Response:', {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries())
      });

      if (!response.ok) {
        // Handle 401 Unauthorized responses
        if (response.status === 401) {
          // Don't redirect during login/register attempts - just throw the error
          if (skipAuthRedirect) {
            const error = await response.json().catch(() => ({ detail: 'Incorrect email or password' }));
            throw new Error(error.detail || 'Incorrect email or password');
          }

          // Try to refresh token and retry request once
          if (retryCount === 0) {
            console.debug('Received 401, attempting token refresh');
            const refreshSuccess = await this.refreshToken();
            if (refreshSuccess) {
              console.debug('Token refreshed, retrying request');
              return this.request<T>(endpoint, options, skipAuthRedirect, retryCount + 1);
            }
          }

          console.debug('Token refresh failed or already retried, logging out user');

          // Set logged out flag and clean up activity tracking
          this.isLoggedOut = true;
          this.cleanupActivityTracking();

          this.safeLocalStorage.removeItem('token');
          this.safeLocalStorage.removeItem('refresh_token');
          this.safeLocalStorage.removeItem('cached_tags');

          // Call the unauthorized handler if set
          if (this.onUnauthorized) {
            this.onUnauthorized();
          }

          // Redirect to home page (which will show login if not authenticated)
          if (typeof window !== 'undefined') {
            window.location.href = '/';
          }
        }

        // Handle 403 Forbidden responses
        if (response.status === 403) {
          console.debug('Access forbidden - user does not have permission to access this resource');
          const error = await response.json().catch(() => ({ detail: 'Access forbidden' }));
          throw new Error(error.detail || 'You do not have permission to access this resource');
        }

        const error = await response.json().catch(() => ({ detail: 'An error occurred' }));
        console.error('API Error:', error);
        throw new Error(error.detail || 'An error occurred');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('API Request Failed:', error);
      throw error;
    }
  }

  // Auth endpoints
  async login(email: string, password: string) {
    const response = await this.request<AuthResponse>('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    }, true); // Skip auth redirect for login attempts

    // Store refresh token
    if (response.refresh_token) {
      this.safeLocalStorage.setItem('refresh_token', response.refresh_token);
    }

    // Reset logged out state and restart activity tracking on successful login
    this.isLoggedOut = false;
    this.setupActivityTracking();

    return response;
  }

  async register(email: string, password: string, full_name?: string) {
    const response = await this.request<AuthResponse>('/auth/register', {
      method: 'POST',
      body: JSON.stringify({ email, password, full_name }),
    }, true); // Skip auth redirect for register attempts

    // Store refresh token
    if (response.refresh_token) {
      this.safeLocalStorage.setItem('refresh_token', response.refresh_token);
    }

    // Reset logged out state and restart activity tracking on successful registration
    this.isLoggedOut = false;
    this.setupActivityTracking();

    return response;
  }

  async getCurrentUser() {
    return this.request<User>('/auth/me');
  }

  async changePassword(currentPassword: string, newPassword: string) {
    return this.request<{ message: string }>('/auth/change-password', {
      method: 'POST',
      body: JSON.stringify({
        current_password: currentPassword,
        new_password: newPassword
      }),
    });
  }

  async getGoogleAuthUrl() {
    return this.request<{ authorization_url: string }>('/auth/google');
  }

  private performIdleLogout() {
    // Prevent multiple logout attempts
    if (this.isLoggedOut) {
      return;
    }

    this.isLoggedOut = true;

    // Clean up all activity tracking
    this.cleanupActivityTracking();

    // Clear local storage
    this.safeLocalStorage.removeItem('token');
    this.safeLocalStorage.removeItem('refresh_token');
    this.safeLocalStorage.removeItem('cached_tags');

    // Call the unauthorized handler to redirect to login
    if (this.onUnauthorized) {
      this.onUnauthorized();
    }

    // Redirect to home page immediately
    if (typeof window !== 'undefined') {
      window.location.href = '/';
    }
  }

  async logout() {
    try {
      // Call backend logout endpoint to revoke refresh tokens
      await this.request<{ message: string }>('/auth/logout', {
        method: 'POST',
      });
    } catch (error) {
      console.error('Error during logout:', error);
      // Continue with local cleanup even if backend call fails
    }

    // Set logged out flag and clean up activity tracking
    this.isLoggedOut = true;
    this.cleanupActivityTracking();

    // Clear local storage
    this.safeLocalStorage.removeItem('token');
    this.safeLocalStorage.removeItem('refresh_token');
    this.safeLocalStorage.removeItem('cached_tags');
  }

  // Projects endpoints
  async getProjects() {
    return this.request<ProjectListItem[]>('/projects/');
  }

  async createProject(project: Partial<Project>) {
    return this.request<Project>('/projects/', {
      method: 'POST',
      body: JSON.stringify(project),
    });
  }

  async getProject(projectId: number) {
    return this.request<Project>(`/projects/${projectId}`);
  }

  async getProjectBySlug(slug: string) {
    return this.request<Project>(`/projects/slug/${slug}`);
  }

  async updateProject(projectId: number, project: Partial<Project>) {
    return this.request<Project>(`/projects/${projectId}`, {
      method: 'PUT',
      body: JSON.stringify(project),
    });
  }

  async deleteProject(projectId: number) {
    return this.request<{ message: string }>(`/projects/${projectId}`, {
      method: 'DELETE',
    });
  }

  // Project variables endpoints
  async getProjectVariables(projectId: number) {
    return this.request<ProjectVariable[]>(`/projects/${projectId}/variables`);
  }

  async createProjectVariable(projectId: number, variable: Partial<ProjectVariable>) {
    return this.request<ProjectVariable>(`/projects/${projectId}/variables`, {
      method: 'POST',
      body: JSON.stringify(variable),
    });
  }

  async updateProjectVariable(projectId: number, variableId: number, variable: Partial<ProjectVariable>) {
    return this.request<ProjectVariable>(`/projects/${projectId}/variables/${variableId}`, {
      method: 'PUT',
      body: JSON.stringify(variable),
    });
  }

  async deleteProjectVariable(projectId: number, variableId: number) {
    return this.request<{ message: string }>(`/projects/${projectId}/variables/${variableId}`, {
      method: 'DELETE',
    });
  }

  // Requirements endpoints
  async getRequirements(projectId: number, limit: number = 1000) {
    return this.request<RequirementListItem[]>(`/requirements/?project_id=${projectId}&limit=${limit}`);
  }

  async createRequirement(requirement: Partial<Requirement> & { project_id: number; tag_names?: string[] }) {
    return this.request<Requirement>('/requirements/', {
      method: 'POST',
      body: JSON.stringify(requirement),
    });
  }

  async getRequirement(requirementId: number) {
    return this.request<Requirement>(`/requirements/${requirementId}`);
  }

  async getRequirementBySlug(projectSlug: string, requirementSlug: string) {
    return this.request<Requirement>(`/requirements/project/${projectSlug}/requirement/${requirementSlug}`);
  }

  async updateRequirement(requirementId: number, requirement: Partial<Requirement>) {
    return this.request<Requirement>(`/requirements/${requirementId}`, {
      method: 'PUT',
      body: JSON.stringify(requirement),
    });
  }

  async deleteRequirement(requirementId: number) {
    return this.request<{ message: string }>(`/requirements/${requirementId}`, {
      method: 'DELETE',
    });
  }

  async refineRequirement(requirementId: number) {
    return this.request<{ message: string; refined_description: string }>(`/requirements/${requirementId}/refine`, {
      method: 'POST',
    });
  }

  async generateTestCases(requirementId: number) {
    return this.request<TestCase[]>(`/requirements/${requirementId}/generate-tests`, {
      method: 'POST',
    });
  }

  // Test cases endpoints
  async getTestCases(requirementId: number) {
    return this.request<TestCase[]>(`/requirements/${requirementId}/test-cases`);
  }

  async createTestCase(requirementId: number, testCase: Partial<TestCase>) {
    return this.request<TestCase>(`/requirements/${requirementId}/test-cases`, {
      method: 'POST',
      body: JSON.stringify(testCase),
    });
  }

  async updateTestCase(requirementId: number, testCaseId: number, testCase: Partial<TestCase>) {
    return this.request<TestCase>(`/requirements/${requirementId}/test-cases/${testCaseId}`, {
      method: 'PUT',
      body: JSON.stringify(testCase),
    });
  }

  async deleteTestCase(requirementId: number, testCaseId: number) {
    return this.request<{ message: string }>(`/requirements/${requirementId}/test-cases/${testCaseId}`, {
      method: 'DELETE',
    });
  }

  async getTags() {
    return this.request<Tag[]>('/requirements/tags/');
  }

  // Code generation endpoints
  async startCodeGeneration(data: {
    requirement_id: number;
    project_id: number;
    automation_framework: string;
    programming_language: string;
    git_url?: string;
    ssh_private_key?: string;
    ssh_passphrase?: string;
    page_elements?: Record<string, unknown>;
  }) {
    return this.request<{ session_id: string; status: string; message: string }>('/code-generation/generate', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async getCodeGenerationSession(sessionId: string) {
    return this.request<{
      session_id: string;
      status: string;
      generation_type: string;
      generated_files_count: number;
      zip_file_path?: string;
      error_message?: string;
      created_at: string;
      started_at?: string;
      completed_at?: string;
      project_id: number;
      requirement_id: number;
    }>(`/code-generation/session/${sessionId}`);
  }

  async downloadGeneratedCode(sessionId: string): Promise<Blob> {
    const url = `${this.baseURL}/code-generation/download/${sessionId}`;
    const token = this.safeLocalStorage.getItem('token');

    const response = await fetch(url, {
      headers: {
        ...(token && { Authorization: `Bearer ${token}` }),
      },
    });

    if (!response.ok) {
      throw new Error('Failed to download generated code');
    }

    return response.blob();
  }

  async cleanupCodeGeneration(sessionId: string) {
    return this.request<{
      session_id: string;
      project_name: string;
      cleanup_results: {
        repository: { success: boolean; message: string };
        embeddings: { success: boolean; message: string };
        temp_files: { success: boolean; removed_files: number; message: string };
      };
      message: string;
    }>(`/code-generation/cleanup/${sessionId}`, {
      method: 'DELETE',
    });
  }

  // Code review methods removed for simplified workflow

  async getUserCodeGenerationSessions() {
    return this.request<Array<{
      session_id: string;
      status: string;
      generation_type: string;
      automation_framework: string;
      programming_language: string;
      generated_files_count: number;
      created_at: string;
      completed_at?: string;
      project_id: number;
      requirement_id: number;
    }>>('/code-generation/sessions/user');
  }

  async getProjectCodeGenerationSessions(projectId: number) {
    return this.request<Array<{
      session_id: string;
      status: string;
      generation_type: string;
      automation_framework: string;
      programming_language: string;
      generated_files_count: number;
      created_at: string;
      completed_at?: string;
      requirement_id: number;
    }>>(`/code-generation/sessions/project/${projectId}`);
  }

  // Project sharing endpoints
  async getProjectMembers(projectId: number) {
    return this.request<ProjectMember[]>(`/projects/${projectId}/members`);
  }

  async addProjectMember(projectId: number, userId: number, role: 'member' = 'member') {
    return this.request<ProjectMember>(`/projects/${projectId}/members`, {
      method: 'POST',
      body: JSON.stringify({ user_id: userId, role }),
    });
  }

  async updateProjectMemberRole(projectId: number, memberId: number, role: 'member') {
    return this.request<ProjectMember>(`/projects/${projectId}/members/${memberId}`, {
      method: 'PUT',
      body: JSON.stringify({ role }),
    });
  }

  async removeProjectMember(projectId: number, memberId: number) {
    return this.request<{ message: string }>(`/projects/${projectId}/members/${memberId}`, {
      method: 'DELETE',
    });
  }

  async searchUsers(query: string) {
    return this.request<UserSearchResult[]>(`/users/search?q=${encodeURIComponent(query)}`);
  }

  // File Import endpoints
  async uploadFile(file: File) {
    const formData = new FormData();
    formData.append('file', file);

    // For file uploads, we need to handle the request differently
    const url = `${this.baseURL}/file-import/upload`;
    const token = this.safeLocalStorage.getItem('token');

    const response = await fetch(url, {
      method: 'POST',
      body: formData,
      headers: {
        ...(token && { Authorization: `Bearer ${token}` }),
        // Don't set Content-Type for FormData - browser will set it with boundary
      },
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ detail: 'Upload failed' }));
      throw new Error(error.detail || 'Upload failed');
    }

    return response.json() as Promise<FileUploadResponse>;
  }

  async getSheetPreview(fileId: string, sheetName: string, previewRows: number = 5, startingRow: number = 1) {
    return this.request<SheetPreview>(`/file-import/preview/${fileId}/${encodeURIComponent(sheetName)}?preview_rows=${previewRows}&starting_row=${startingRow}`);
  }

  async importTestCases(projectId: number, importRequest: ImportRequest) {
    console.log('Importing test cases with request:', importRequest);
    return this.request<ImportResponse>(`/file-import/import/${projectId}`, {
      method: 'POST',
      body: JSON.stringify(importRequest),
    });
  }

  async cleanupFile(fileId: string) {
    return this.request<{ message: string }>(`/file-import/cleanup/${fileId}`, {
      method: 'DELETE',
    });
  }

  async validateColumnMapping(fileId: string, sheetName: string, columnMapping: ColumnMapping) {
    const params = new URLSearchParams({
      sheet_name: sheetName,
      ...Object.fromEntries(
        Object.entries(columnMapping).map(([key, value]) => [key, String(value)])
      )
    });

    return this.request<{
      valid: boolean;
      errors: string[];
      available_columns: string[];
    }>(`/file-import/validate-mapping/${fileId}?${params}`);
  }
}

export const apiClient = new ApiClient(API_BASE_URL);
