'use client';

import { useState, useCallback, memo } from 'react';
import Image from 'next/image';
// import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
// import { Input } from '@/components/ui/input';
// import { Label } from '@/components/ui/label';
// import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogBody, DialogFooter } from '@/components/ui/dialog';
// import { ArrowRight, Eye, EyeOff } from 'lucide-react';
import { apiClient } from '@/lib/api';
// import { useToast } from '@/hooks/useToast';
// import { ToastContainer } from '@/components/ui/toast';

function AuthPageComponent() {
  // const [isLogin, setIsLogin] = useState(true);
  // const [email, setEmail] = useState('');
  // const [password, setPassword] = useState('');
  // const [fullName, setFullName] = useState('');

  // const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  // const [showForgotPassword, setShowForgotPassword] = useState(false);
  // const [forgotPasswordEmail, setForgotPasswordEmail] = useState('');
  // const [forgotPasswordLoading, setForgotPasswordLoading] = useState(false);
  // const [forgotPasswordMessage, setForgotPasswordMessage] = useState('');
  // const [showPassword, setShowPassword] = useState(false);
  // const [validationErrors, setValidationErrors] = useState({
  //   fullName: '',
  //   email: '',
  //   password: ''
  // });

  // const { login, register } = useAuth();
  // const { toasts, showToast, removeToast } = useToast();

  // const validateForm = useCallback(() => {
  //   const errors = { fullName: '', email: '', password: '' };
  //   let isValid = true;

  //   // Validate full name for signup
  //   if (!isLogin) {
  //     if (!fullName.trim()) {
  //       errors.fullName = 'Full name is required';
  //       isValid = false;
  //     } else if (fullName.trim().length < 2) {
  //       errors.fullName = 'Full name must be at least 2 characters';
  //       isValid = false;
  //     }
  //   }

  //   // Validate email
  //   if (!email.trim()) {
  //     errors.email = 'Email is required';
  //     isValid = false;
  //   } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
  //     errors.email = 'Please enter a valid email address';
  //     isValid = false;
  //   }

  //   // Validate password
  //   if (!password) {
  //     errors.password = 'Password is required';
  //     isValid = false;
  //   } else if (password.length < 6) {
  //     errors.password = 'Password must be at least 6 characters';
  //     isValid = false;
  //   }

  //   setValidationErrors(errors);
  //   return isValid;
  // }, [isLogin, fullName, email, password]);

  // const handleSubmit = useCallback(async (e: React.FormEvent) => {
  //   e.preventDefault();
  //   setLoading(true);
  //   setError('');

  //   // Validate form before submission
  //   if (!validateForm()) {
  //     setLoading(false);
  //     return;
  //   }

  //   try {
  //     if (isLogin) {
  //       await login(email, password);
  //     } else {
  //       await register(email, password, fullName);
  //     }
  //   } catch (error) {
  //     setError(error instanceof Error ? error.message : 'An error occurred');
  //   } finally {
  //     setLoading(false);
  //   }
  // }, [isLogin, email, password, fullName, login, register, validateForm]);

  // const handleInputChange = useCallback((field: string, value: string) => {
  //   // Update the field value
  //   switch (field) {
  //     case 'fullName':
  //       setFullName(value);
  //       break;
  //     case 'email':
  //       setEmail(value);
  //       break;
  //     case 'password':
  //       setPassword(value);
  //       break;
  //   }

  //   // Clear validation error for this field when user starts typing
  //   if (validationErrors[field as keyof typeof validationErrors]) {
  //     setValidationErrors(prev => ({
  //       ...prev,
  //       [field]: ''
  //     }));
  //   }

  //   // Clear general error when user starts typing
  //   if (error) {
  //     setError('');
  //   }
  // }, [validationErrors, error]);

  // const handleToggleMode = useCallback(() => {
  //   setIsLogin(!isLogin);
  //   setError('');
  //   setValidationErrors({ fullName: '', email: '', password: '' });
  // }, [isLogin]);

  const handleGoogleLogin = useCallback(async () => {
    try {
      const response = await apiClient.getGoogleAuthUrl();
      window.location.href = response.authorization_url;
    } catch (error) {
      console.debug('Google login error:', error);
      setError('Failed to initiate Google login');
    }
  }, []);

  // const handleForgotPassword = async (e: React.FormEvent) => {
  //   e.preventDefault();
  //   setForgotPasswordLoading(true);
  //   setForgotPasswordMessage('');

  //   try {
  //     const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';

  //     // First, check if the user exists and their auth provider
  //     const checkUserResponse = await fetch(`${API_BASE_URL}/auth/check-user-auth-provider`, {
  //       method: 'POST',
  //       headers: {
  //         'Content-Type': 'application/json',
  //       },
  //       body: JSON.stringify({ email: forgotPasswordEmail }),
  //     });

  //     if (checkUserResponse.ok) {
  //       const userData = await checkUserResponse.json();

  //       // If user uses Google SSO, show appropriate message
  //       if (userData.auth_provider === 'google') {
  //         setShowForgotPassword(false);
  //         setForgotPasswordEmail('');
  //         setForgotPasswordMessage('');
  //         showToast('You are using Google Sign-In. Please use "Sign in with Google" to access your account.', 'info');
  //         return;
  //       }
  //     }

  //     // Proceed with normal forgot password flow for email users
  //     const response = await fetch(`${API_BASE_URL}/auth/forgot-password`, {
  //       method: 'POST',
  //       headers: {
  //         'Content-Type': 'application/json',
  //       },
  //       body: JSON.stringify({ email: forgotPasswordEmail }),
  //     });

  //     if (response.ok) {
  //       // Close modal and show success toast
  //       setShowForgotPassword(false);
  //       setForgotPasswordEmail('');
  //       setForgotPasswordMessage('');
  //       showToast('Password reset instructions have been sent to your email.', 'success');
  //     } else {
  //       const errorData = await response.json();
  //       setForgotPasswordMessage(errorData.detail || 'Failed to send reset email. Please try again.');
  //     }
  //   } catch (error) {
  //     console.debug('Forgot password error:', error);
  //     setForgotPasswordMessage('An error occurred. Please try again.');
  //   } finally {
  //     setForgotPasswordLoading(false);
  //   }
  // };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-100 via-purple-50 to-purple-200 flex items-center justify-center p-4">
      {/* Background decorative elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-1/2 right-1/4 transform -translate-y-1/2 hidden lg:block">
          <div className="w-96 h-96 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full opacity-80 blur-3xl"></div>
        </div>
        {/* Mobile background decoration */}
        <div className="absolute -top-20 -right-20 lg:hidden">
          <div className="w-64 h-64 bg-gradient-to-br from-purple-400 to-purple-600 rounded-full opacity-60 blur-2xl"></div>
        </div>
        <div className="absolute -bottom-20 -left-20 lg:hidden">
          <div className="w-48 h-48 bg-gradient-to-br from-purple-300 to-purple-500 rounded-full opacity-40 blur-xl"></div>
        </div>
      </div>

      {/* Main content card */}
      <main id="main-content" className="relative bg-white rounded-2xl lg:rounded-3xl shadow-2xl w-full max-w-sm lg:max-w-4xl min-h-[600px] flex flex-col lg:flex-row overflow-hidden">
        <div className="flex-1 p-8 lg:p-12 flex flex-col justify-center">
          <div className="w-full max-w-sm mx-auto lg:mx-0 lg:max-w-md">
            {/* Logo and Welcome Header */}
            <div className="mb-10 text-left">
              <div className="inline-block mb-6">
                 <div className="w-16 h-16 rounded-2xl flex items-center justify-center">
                    <Image
                      src="/favicon.ico"
                      alt="IntelliTest Logo"
                      width={60}
                      height={60}
                      className="rounded-md"
                    />
                 </div>
              </div>
              <h2 className="text-3xl lg:text-4xl font-extrabold text-gray-900 tracking-tight">
                Welcome to IntelliTest
              </h2>
              <p className="mt-4 text-lg text-gray-600">
                Sign in to start generating intelligent tests automatically.
              </p>
            </div>

            {/* Google Login Button */}
            <div className="space-y-4">
              <Button
                type="button"
                onClick={handleGoogleLogin}
                className="w-full bg-white hover:bg-gray-50 text-gray-900 border-2 border-gray-200 hover:border-gray-300 py-4 px-6 rounded-xl font-semibold transition-all duration-200 flex items-center justify-center gap-4 shadow-sm hover:shadow-md transform hover:scale-[1.02]"
              >
                <svg width="28" height="28" viewBox="0 0 24 24">
                  <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                  <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                  <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                  <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
                <span className="text-sm font-normal">Continue with Google</span>
              </Button>
            </div>

            {/* Divider */}
            <div className="my-8 flex items-center">
              <div className="flex-grow border-t border-gray-200"></div>
              <span className="flex-shrink mx-4 text-xs font-medium text-gray-400">Secure Sign-in</span>
              <div className="flex-grow border-t border-gray-200"></div>
            </div>
          </div>
        </div>

        {/* Right side - Decorative */}
        <div className="hidden lg:flex flex-1 relative bg-gray-50 items-center justify-center overflow-hidden">
          {/* Banner Image */}
          <div className="relative w-full h-full">
            <Image
              src="/banner.webp"
              alt="IntelliTest Banner"
              fill
              className="object-cover"
              priority
            />
          </div>
        </div>
      </main>

      {/* Footer */}
      <div className="absolute bottom-4 left-4 lg:bottom-8 lg:left-8 text-xs lg:text-sm text-gray-500">
        © IntelliTest 2025
      </div>
    </div>
  );
}

export const AuthPage = memo(AuthPageComponent);
