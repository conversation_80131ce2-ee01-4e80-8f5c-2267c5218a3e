version: '3.8'

services:
  # Qdrant Vector Database
  qdrant:
    image: qdrant/qdrant:latest
    container_name: intellitest_qdrant_dev
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data_dev:/qdrant/storage
    healthcheck:
      test: ["CMD", "sh", "-c", "exec 3<>/dev/tcp/localhost/6333"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # Ollama LLM Service
  ollama:
    image: ollama/ollama:latest
    container_name: intellitest_ollama_dev
    ports:
      - "11434:11434"
    volumes:
      - ollama_data_dev:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--spider", "--tries=1", "http://localhost:11434/api/version"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s

  # Backend API (Development)
  backend:
    build:
      context: ./backend
      target: development
    container_name: intellitest_backend_dev
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
      - /app/venv
    environment:
      # Database connection to external PostgreSQL
      - POSTGRES_SERVER=${POSTGRES_SERVER:-localhost}
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-password}
      - POSTGRES_DB=${POSTGRES_DB:-intellitest}
      - POSTGRES_PORT=${POSTGRES_PORT:-5432}
      # Vector database
      - QDRANT_HOST=qdrant
      - QDRANT_PORT=6333
      # LLM service
      - OLLAMA_BASE_URL=http://ollama:11434
      - OLLAMA_MODEL=${OLLAMA_MODEL:-llama3.2:latest}
      # Security
      - SECRET_KEY=${SECRET_KEY:-dev-secret-key-change-in-production}
    depends_on:
      qdrant:
        condition: service_healthy
      ollama:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Frontend (Development)
  frontend:
    build:
      context: ./frontend
      target: development
    container_name: intellitest_frontend_dev
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
      - /app/.next
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000/api/v1
    depends_on:
      backend:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 5

volumes:
  qdrant_data_dev:
  ollama_data_dev:
