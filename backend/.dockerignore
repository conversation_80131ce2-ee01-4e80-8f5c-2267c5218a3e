# Python artifacts
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
env/
pip-log.txt
pip-delete-this-directory.txt

# Test and coverage artifacts
.tox/
.coverage
.coverage.*
.cache
.pytest_cache
.hypothesis/
nosetests.xml
coverage.xml
*.cover
*.log
test.db
*.db

# Git and MyPy
.git/
.mypy_cache/

# Virtual environments
venv/
ENV/
.venv/

# Environment files
*.env

# IDE configuration
.vscode/
.idea/
*.swp
*.swo
*~

# OS-specific files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Docker files
Dockerfile
.dockerignore

# Documentation
*.md