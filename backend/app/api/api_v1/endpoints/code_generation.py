import uuid
import asyncio
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks, Body, Request
from pydantic import BaseModel
from fastapi.responses import StreamingResponse, FileResponse
from sqlalchemy.orm import Session
import json
import logging

from app.api.deps import get_db, get_current_active_user
from app.models.user import User
from app.models.project import Project, AutomationFramework, ProgrammingLanguage
from app.models.requirement import Requirement
from app.models.code_generation import CodeGenerationSession, CodeGenerationStatus, CodeGenerationType
from app.schemas.code_generation import (
    CodeGenerationRequest, CodeGenerationProgress, CodeGenerationSummary,
    RepositoryAnalysis, CodeGenerationStats
)
from app.crud.project import get_project, is_project_member
from app.crud.requirement import get_requirement as crud_get_requirement
from app.crud.test_case import get_test_cases_for_requirement
from app.models.test_case import TestCase
from app.services.git_service import git_service
from app.services.code_generation_service import code_generation_service
from app.services.code_embedding_service import code_embedding_service
from app.services.file_management_service import file_management_service
from app.services.streaming_service import streaming_store
from app.crud.code_generation import (
    create_code_generation_session,
    get_code_generation_session,
    update_code_generation_session,
    get_sessions_for_project,
    get_sessions_for_user
)

logger = logging.getLogger(__name__)
router = APIRouter()

# Code review functionality removed for simplified workflow

@router.get("/test-auth")
async def test_authentication(
    current_user: User = Depends(get_current_active_user)
):
    """Test endpoint to verify authentication is working."""
    logger.debug(f"Auth test endpoint accessed by user {current_user.id}")
    return {"message": "Authentication working", "user_id": current_user.id, "email": current_user.email}

@router.post("/generate", response_model=Dict[str, Any])
async def start_code_generation(
    request: CodeGenerationRequest,
    background_tasks: BackgroundTasks,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Start code generation for a requirement.
    
    This endpoint initiates the code generation process and returns a session ID
    that can be used to track progress via the streaming endpoint.
    """
    try:
        # Validate project access
        project = get_project(db, request.project_id)
        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Project not found"
            )
        
        if not is_project_member(db, request.project_id, current_user.id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to access this project"
            )
        
        # Validate requirement
        requirement = crud_get_requirement(db, request.requirement_id)
        if not requirement or requirement.project_id != request.project_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Requirement not found or not in this project"
            )
        
        # Get test cases for the requirement
        test_cases = get_test_cases_for_requirement(db, request.requirement_id)
        if not test_cases:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No test cases found for this requirement. Please generate test cases first."
            )
        
        # Validate automation framework and language
        try:
            framework = AutomationFramework(request.automation_framework)
            language = ProgrammingLanguage(request.programming_language)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Invalid automation framework or programming language"
            )
        
        # Use project's git URL if not provided in request (optional for standalone generation)
        git_url = request.git_url or project.code_source
        
        # Generate session ID
        session_id = str(uuid.uuid4())
        
        # Create session record
        session_data = {
            "project_id": request.project_id,
            "requirement_id": request.requirement_id,
            "user_id": current_user.id,
            "session_id": session_id,
            "generation_type": CodeGenerationType.STANDALONE if not git_url else CodeGenerationType.EMPTY_REPO,  # Will be determined during processing
            "git_url": git_url,
            "automation_framework": framework.value,
            "programming_language": language.value,
            "test_cases_data": [
                {
                    "id": tc.id,
                    "title": tc.title,
                    "steps": tc.steps,
                    "expected_result": tc.expected_result,
                    "notes": tc.notes
                }
                for tc in test_cases
            ],
            "page_elements_data": request.page_elements
        }
        
        create_code_generation_session(db, session_data)

        # Pre-create stream for the session to avoid race condition
        streaming_store.create_stream(session_id)
        logger.info(f"Pre-created stream for session {session_id}")

        # Start background task for code generation
        background_tasks.add_task(
            _process_code_generation,
            session_id,
            request,
            test_cases,
            framework,
            language,
            current_user.id
        )
        
        return {
            "session_id": session_id,
            "status": "started",
            "message": "Code generation started. Use the session ID to track progress."
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting code generation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start code generation"
        )

@router.get("/stream/{session_id}")
async def stream_code_generation_progress(
    session_id: str,
    token: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """
    Stream code generation progress in real-time.

    This endpoint provides Server-Sent Events (SSE) for real-time updates
    on the code generation process.
    """
    try:
        # Authenticate user via token parameter (EventSource can't send headers)
        if not token:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token is required for streaming endpoint"
            )

        # Verify token and get user
        from app.core.security import verify_token
        from app.crud.user import get_user

        user_id = verify_token(token)
        if user_id is None:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid or expired token"
            )

        current_user = get_user(db, user_id=int(user_id))
        if not current_user or not current_user.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not found or inactive"
            )

        # Validate session access
        session = get_code_generation_session(db, session_id)
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Code generation session not found"
            )

        if session.user_id != current_user.id:
            # Check if user has access to the project
            if not is_project_member(db, session.project_id, current_user.id):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Not authorized to access this session"
                )
        
        async def generate_progress_stream():
            """Generate SSE stream for code generation progress."""
            logger.info(f"GENERATOR FUNCTION CALLED for session {session_id}")
            try:
                # Create stream in the store if it doesn't exist (it should already exist from the start endpoint)
                if session_id not in streaming_store._streams:
                    streaming_store.create_stream(session_id)
                    logger.info(f"Created stream for session {session_id} (fallback)")
                else:
                    logger.info(f"Using existing stream for session {session_id}")

                # Send initial status
                initial_data = {
                    "session_id": session_id,
                    "type": "status",
                    "status": session.status.value,
                    "message": "Connecting to code generation stream..."
                }
                logger.info(f"Sending initial data to stream {session_id}: {initial_data}")
                yield f"data: {json.dumps(initial_data)}\n\n"

                # Stream real-time progress
                logger.info(f"Starting progress stream loop for session {session_id}")
                async for progress in streaming_store.get_progress_stream(session_id):
                    if progress.get("type") == "keepalive":
                        # Send keepalive to prevent connection timeout
                        logger.debug(f"Sending keepalive for session {session_id}")
                        continue

                    # Add session_id to progress data
                    progress["session_id"] = session_id

                    yield f"data: {json.dumps(progress)}\n\n"

                    # Break on completion or critical errors - stream closes gracefully
                    if progress.get("type") in ["stream_end", "error"] or progress.get("status") in ["failed"]:
                        logger.info(f"Breaking stream loop for session {session_id} on {progress.get('type')}")
                        break

                logger.info(f"Finished progress stream loop for session {session_id}")

            except Exception as e:
                logger.error(f"Error in progress stream: {e}")
                error_data = {
                    "session_id": session_id,
                    "type": "error",
                    "status": "error",
                    "message": f"Stream error: {str(e)}",
                    "error": True
                }
                yield f"data: {json.dumps(error_data)}\n\n"
            finally:
                # Cleanup stream
                streaming_store.cleanup_stream(session_id)
        
        return StreamingResponse(
            generate_progress_stream(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Headers": "*"
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error setting up progress stream: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to set up progress stream"
        )


# Code review endpoint removed for simplified workflow


# Code review application endpoints removed for simplified workflow


# Code review background tasks removed for simplified workflow

@router.get("/session/{session_id}", response_model=Dict[str, Any])
def get_session_status(
    session_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get the current status of a code generation session."""
    try:
        session = get_code_generation_session(db, session_id)
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Code generation session not found"
            )
        
        # Check access permissions
        if session.user_id != current_user.id:
            if not is_project_member(db, session.project_id, current_user.id):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Not authorized to access this session"
                )
        
        return {
            "session_id": session.session_id,
            "status": session.status.value,
            "generation_type": session.generation_type.value,
            "generated_files_count": session.generated_files_count,
            "zip_file_path": session.zip_file_path,
            "error_message": session.error_message,
            "created_at": session.created_at.isoformat(),
            "started_at": session.started_at.isoformat() if session.started_at else None,
            "completed_at": session.completed_at.isoformat() if session.completed_at else None,
            "project_id": session.project_id,
            "requirement_id": session.requirement_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting session status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get session status"
        )

@router.post("/test-ssh-key")
def test_ssh_key_format(
    request: Dict[str, str],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Test SSH private key format without performing git operations."""
    try:
        ssh_private_key = request.get("ssh_private_key")
        if not ssh_private_key:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="SSH private key is required"
            )

        # Test the SSH key format
        from app.services.git_service import git_service
        test_result = git_service.test_ssh_key_format(ssh_private_key)

        return {
            "valid": test_result["valid"],
            "error": test_result["error"],
            "suggestions": test_result["suggestions"],
            "key_info": test_result["key_info"]
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error testing SSH key: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to test SSH key format"
        )

@router.get("/download/{session_id}")
async def download_generated_code(
    session_id: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Download the generated code as a zip file."""
    try:
        session = get_code_generation_session(db, session_id)
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Code generation session not found"
            )
        
        # Check access permissions
        if session.user_id != current_user.id:
            if not is_project_member(db, session.project_id, current_user.id):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Not authorized to access this session"
                )
        
        # Check if generation is completed
        if session.status != CodeGenerationStatus.COMPLETED:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Code generation is not completed yet"
            )
        
        # Check if zip file exists
        if not session.zip_file_path:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Generated code file not found"
            )
        
        import os
        if not os.path.exists(session.zip_file_path):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Generated code file no longer exists"
            )
        
        # Get project name for filename
        project = get_project(db, session.project_id)
        filename = f"{project.name}_generated_code.zip" if project else "generated_code.zip"
        
        return FileResponse(
            path=session.zip_file_path,
            filename=filename,
            media_type="application/zip"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error downloading generated code: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to download generated code"
        )

@router.get("/sessions/project/{project_id}", response_model=List[Dict[str, Any]])
def get_project_sessions(
    project_id: int,
    skip: int = 0,
    limit: int = 200,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get code generation sessions for a project."""
    try:
        # Check project access
        if not is_project_member(db, project_id, current_user.id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to access this project"
            )

        sessions = get_sessions_for_project(db, project_id, skip=skip, limit=limit)

        return [
            {
                "session_id": session.session_id,
                "status": session.status.value,
                "generation_type": session.generation_type.value,
                "automation_framework": session.automation_framework,
                "programming_language": session.programming_language,
                "generated_files_count": session.generated_files_count,
                "created_at": session.created_at.isoformat(),
                "completed_at": session.completed_at.isoformat() if session.completed_at else None,
                "requirement_id": session.requirement_id
            }
            for session in sessions
        ]

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting project sessions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get project sessions"
        )

@router.get("/sessions/user", response_model=List[Dict[str, Any]])
def get_user_sessions(
    skip: int = 0,
    limit: int = 200,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Get code generation sessions for the current user."""
    try:
        sessions = get_sessions_for_user(db, current_user.id, skip=skip, limit=limit)

        return [
            {
                "session_id": session.session_id,
                "status": session.status.value,
                "generation_type": session.generation_type.value,
                "automation_framework": session.automation_framework,
                "programming_language": session.programming_language,
                "generated_files_count": session.generated_files_count,
                "created_at": session.created_at.isoformat(),
                "completed_at": session.completed_at.isoformat() if session.completed_at else None,
                "project_id": session.project_id,
                "requirement_id": session.requirement_id
            }
            for session in sessions
        ]

    except Exception as e:
        logger.error(f"Error getting user sessions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user sessions"
        )

@router.post("/analyze-repository/{project_id}", response_model=RepositoryAnalysis)
async def analyze_repository(
    project_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Analyze a project's repository structure and content."""
    try:
        # Check project access
        project = get_project(db, project_id)
        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Project not found"
            )

        if not is_project_member(db, project_id, current_user.id):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not authorized to access this project"
            )

        if not project.code_source:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No Git repository URL configured for this project"
            )

        # Clone repository if not already cloned
        clone_result = git_service.clone_repository(project.code_source, project.name)

        if not clone_result["success"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Failed to clone repository: {clone_result.get('error', 'Unknown error')}"
            )

        # Get repository files
        repo_files = git_service.get_repository_files(project.name)

        # Analyze files
        analysis = _analyze_repository_files(repo_files)
        analysis["is_empty"] = clone_result["is_empty"]
        analysis["structure"] = await file_management_service.get_project_structure(project.name)

        return RepositoryAnalysis(**analysis)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing repository: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to analyze repository"
        )

@router.delete("/cleanup/{session_id}")
@router.post("/cleanup/{session_id}")  # Also accept POST for sendBeacon compatibility
async def cleanup_code_generation(
    session_id: str,
    request: Request,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Clean up repository and embeddings after code generation download."""
    try:
        session = get_code_generation_session(db, session_id)
        if not session:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Code generation session not found"
            )

        # Check access permissions
        if session.user_id != current_user.id:
            if not is_project_member(db, session.project_id, current_user.id):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Not authorized to access this session"
                )

        # Get project information
        project = get_project(db, session.project_id)
        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Project not found"
            )

        cleanup_results = {}

        # Force cleanup the stream (modal close, download, or tab close)
        # Note: Stream may already be closed if generation completed successfully
        try:
            if streaming_store.is_stream_active(session_id):
                streaming_store.force_cleanup_stream(session_id)
                cleanup_results["stream"] = {
                    "success": True,
                    "message": "Active stream cleaned up successfully"
                }
            else:
                cleanup_results["stream"] = {
                    "success": True,
                    "message": "Stream already closed (generation completed)"
                }
        except Exception as e:
            logger.error(f"Error cleaning up stream for session {session_id}: {e}")
            cleanup_results["stream"] = {
                "success": False,
                "error": str(e),
                "message": "Failed to clean up stream"
            }

        # Clean up repository folder for this specific project only
        try:
            repo_cleanup = git_service.cleanup_project(project.name)
            cleanup_results["repository"] = {
                "success": repo_cleanup,
                "message": "Repository cleaned up successfully" if repo_cleanup else "Failed to clean up repository"
            }
        except Exception as e:
            logger.error(f"Error cleaning up repository for project {project.name}: {e}")
            cleanup_results["repository"] = {
                "success": False,
                "error": str(e),
                "message": "Failed to clean up repository"
            }

        # Clean up embeddings
        try:
            embedding_cleanup = code_embedding_service.delete_project_embeddings(project.name)
            cleanup_results["embeddings"] = {
                "success": embedding_cleanup,
                "message": "Embeddings cleaned up successfully" if embedding_cleanup else "Failed to clean up embeddings"
            }
        except Exception as e:
            logger.error(f"Error cleaning up embeddings for project {project.name}: {e}")
            cleanup_results["embeddings"] = {
                "success": False,
                "error": str(e),
                "message": "Failed to clean up embeddings"
            }

        # Clean up temporary files
        try:
            temp_cleanup = await file_management_service.cleanup_temp_files(older_than_hours=0)
            cleanup_results["temp_files"] = {
                "success": temp_cleanup["success"],
                "removed_files": temp_cleanup.get("removed_files", 0),
                "message": f"Cleaned up {temp_cleanup.get('removed_files', 0)} temporary files"
            }
        except Exception as e:
            logger.error(f"Error cleaning up temporary files: {e}")
            cleanup_results["temp_files"] = {
                "success": False,
                "error": str(e),
                "message": "Failed to clean up temporary files"
            }

        # Update session to mark as cleaned up
        try:
            update_code_generation_session(
                db, session_id,
                {"cleaned_up_at": "now"}
            )
        except Exception as e:
            logger.warning(f"Failed to update session cleanup status: {e}")

        logger.info(f"Cleanup completed for session {session_id}, project {project.name}")

        return {
            "session_id": session_id,
            "project_name": project.name,
            "cleanup_results": cleanup_results,
            "message": "Cleanup completed"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error during cleanup for session {session_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cleanup code generation resources"
        )

@router.post("/cleanup/manual")
async def manual_cleanup(
    project_name: str = None,
    current_user: User = Depends(get_current_active_user)
):
    """Manually trigger cleanup of orphaned projects and temporary files."""
    try:
        from app.services.cleanup_service import cleanup_service

        result = await cleanup_service.manual_cleanup(project_name)

        if result["success"]:
            return result
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.get("message", "Manual cleanup failed")
            )

    except Exception as e:
        logger.error(f"Error in manual cleanup: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to perform manual cleanup"
        )

async def _process_code_generation(
    session_id: str,
    request: CodeGenerationRequest,
    test_cases: List,
    framework: AutomationFramework,
    language: ProgrammingLanguage,
    user_id: int
):
    """Background task to process code generation."""
    from app.db.session import SessionLocal
    import asyncio

    # Add a small delay to ensure EventSource has time to connect
    await asyncio.sleep(1.0)
    logger.info(f"Starting code generation background task for session {session_id}")

    db = SessionLocal()
    try:
        # Get session
        session = get_code_generation_session(db, session_id)
        if not session:
            logger.error(f"Session {session_id} not found")
            return

        # Update session status to in progress
        update_code_generation_session(
            db, session_id,
            {"status": CodeGenerationStatus.IN_PROGRESS, "started_at": "now"}
        )

        # Handle repository cloning or standalone generation
        project = get_project(db, request.project_id)
        generation_type = CodeGenerationType.STANDALONE

        if request.git_url:
            # Clone repository if Git URL is provided
            logger.info(f"Cloning repository for session {session_id}")
            clone_result = git_service.clone_repository(
                request.git_url,
                project.name,
                ssh_private_key=request.ssh_private_key,
                ssh_passphrase=request.ssh_passphrase
            )

            if not clone_result["success"]:
                update_code_generation_session(
                    db, session_id,
                    {
                        "status": CodeGenerationStatus.FAILED,
                        "error_message": f"Failed to clone repository: {clone_result.get('error')}",
                        "completed_at": "now"
                    }
                )
                return

            # Validate repository compatibility if not empty
            if not clone_result.get("is_empty", True):
                logger.info(f"Validating repository compatibility for session {session_id}")

                from app.services.repository_validation_service import repository_validation_service

                try:
                    validation_result = await repository_validation_service.validate_repository_compatibility(
                        project.name, framework, language
                    )

                    # If repository is incompatible, fail the generation
                    if not validation_result["is_compatible"]:
                        # Clean up cloned repository
                        git_service.cleanup_project(project.name)

                        detected_frameworks = [fw["framework"].value for fw in validation_result.get("detected_frameworks", [])]
                        detected_languages = [lang["language"].value for lang in validation_result.get("detected_languages", [])]

                        error_message = f"Repository incompatibility detected!\n\n"
                        error_message += f"Expected: {framework.value} + {language.value}\n"

                        if detected_frameworks:
                            error_message += f"Detected frameworks: {', '.join(detected_frameworks)}\n"
                        if detected_languages:
                            error_message += f"Detected languages: {', '.join(detected_languages)}\n"

                        error_message += f"\nRecommendations:\n"
                        for rec in validation_result.get("recommendations", []):
                            error_message += f"• {rec}\n"

                        update_code_generation_session(
                            db, session_id,
                            {
                                "status": CodeGenerationStatus.FAILED,
                                "error_message": error_message,
                                "completed_at": "now"
                            }
                        )

                        # Send validation error to stream
                        await streaming_store.add_progress(session_id, {
                            "type": "validation_error",
                            "message": "Repository framework/language mismatch",
                            "details": error_message,
                            "validation_result": validation_result
                        })
                        return

                    # Send validation success to stream
                    await streaming_store.add_progress(session_id, {
                        "type": "validation_success",
                        "message": f"✅ Repository validated: {framework.value} + {language.value}",
                        "confidence_score": validation_result.get("confidence_score", 0.0)
                    })

                except Exception as e:
                    logger.warning(f"Repository validation failed for session {session_id}: {e}")
                    # Continue with generation even if validation fails
                    await streaming_store.add_progress(session_id, {
                        "type": "validation_warning",
                        "message": f"⚠️ Could not validate repository compatibility: {str(e)}"
                    })

            # Determine generation type based on repository state
            generation_type = CodeGenerationType.EMPTY_REPO if clone_result["is_empty"] else CodeGenerationType.EXISTING_REPO
        else:
            # Standalone generation - create project directory structure
            logger.info(f"Starting standalone code generation for session {session_id}")
            project_path = git_service.get_project_path(project.name)
            repo_path = git_service.get_repo_path(project.name)

            # Create project directories
            project_path.mkdir(parents=True, exist_ok=True)
            repo_path.mkdir(parents=True, exist_ok=True)

            logger.info(f"Created standalone project directory: {repo_path}")

        # Update session with determined generation type
        update_code_generation_session(db, session_id, {"generation_type": generation_type})

        # Prepare data for generation
        requirement = crud_get_requirement(db, request.requirement_id)
        page_elements = request.page_elements or {}

        generated_files = []

        if generation_type == CodeGenerationType.EMPTY_REPO or generation_type == CodeGenerationType.STANDALONE:
            # Generate code for empty repository or standalone generation
            logger.info(f"Generating code for {'empty repository' if generation_type == CodeGenerationType.EMPTY_REPO else 'standalone project'} - session {session_id}")

            async for progress in code_generation_service.generate_code_for_empty_repo(
                project_name=project.name,
                requirement_name=requirement.name,
                test_cases=test_cases,
                page_elements=page_elements,
                automation_framework=framework,
                programming_language=language,
                project_context=project.description
            ):
                # Check if session was cancelled before forwarding progress
                if streaming_store.is_session_cancelled(session_id):
                    logger.info(f"Session {session_id} was cancelled, stopping code generation")
                    break

                # Forward progress to streaming store
                await streaming_store.add_progress(session_id, progress)

                if progress["type"] == "file":
                    generated_files.append(progress["data"])
                elif progress["type"] == "file_complete":
                    # Also add to generated_files for final processing
                    if "data" in progress:
                        generated_files.append(progress["data"])
                elif progress["type"] == "error":
                    update_code_generation_session(
                        db, session_id,
                        {
                            "status": CodeGenerationStatus.FAILED,
                            "error_message": progress["message"],
                            "completed_at": "now"
                        }
                    )
                    return
                # When explanation is complete, trigger file operations but keep stream open
                if progress["type"] == "explanation_complete":
                    logger.info(f"Explanation complete for {session_id}, starting file operations...")

                    # Save generated files
                    logger.info(f"Saving {len(generated_files)} generated files - session {session_id}")
                    save_result = await file_management_service.save_generated_files(project.name, generated_files)

                    if save_result["success"]:
                        # Create zip file
                        logger.info(f"Creating zip file - session {session_id}")
                        zip_path = await file_management_service.create_project_zip(project.name)

                        if zip_path:
                            # Update session as completed
                            update_code_generation_session(
                                db, session_id,
                                {
                                    "status": CodeGenerationStatus.COMPLETED,
                                    "generated_files_count": len(generated_files),
                                    "zip_file_path": zip_path,
                                    "completed_at": "now"
                                }
                            )
                            logger.info(f"File operations completed for {session_id}, stream will close automatically")

                    # Stream will close automatically after explanation_complete event

        else:
            # Generate code for existing repository
            logger.info(f"Generating code for existing repository - session {session_id}")

            # Embed existing code
            embedding_result = code_embedding_service.embed_repository(project.id, project.name, language)
            if not embedding_result["success"]:
                logger.warning(f"Failed to embed repository: {embedding_result.get('error')}")

            # Get enhanced code context for incremental generation
            existing_code_context = code_embedding_service.get_code_context_for_generation(
                project.id, requirement.description, language
            )

            # Also get detailed context for better incremental generation
            detailed_context = await code_embedding_service.get_detailed_code_context_for_incremental_generation(
                project_id=project.id,
                project_name=project.name,
                requirement_id=requirement.id,
                requirement_name=requirement.name,
                new_test_cases=test_cases,
                language=language
            )

            # Log the analysis for debugging
            logger.info(f"Detailed context analysis for {project.name}: {detailed_context.get('generation_strategy', {})}")
            if detailed_context.get('existing_test_cases'):
                logger.info(f"Found {len(detailed_context['existing_test_cases'])} existing test cases")
            if detailed_context.get('target_test_file'):
                logger.info(f"Target test file: {detailed_context['target_test_file'].get('file_path', 'None')}")

            async for progress in code_generation_service.generate_code_for_existing_repo(
                project_id=project.id,
                project_name=project.name,
                requirement_id=requirement.id,
                requirement_name=requirement.name,
                test_cases=test_cases,
                page_elements=page_elements,
                automation_framework=framework,
                programming_language=language,
                existing_code_context=existing_code_context
            ):
                # Check if session was cancelled before forwarding progress
                if streaming_store.is_session_cancelled(session_id):
                    logger.info(f"Session {session_id} was cancelled, stopping code generation")
                    break

                # Forward progress to streaming store
                await streaming_store.add_progress(session_id, progress)

                if progress["type"] == "file":
                    generated_files.append(progress["data"])
                elif progress["type"] == "file_complete":
                    # Also add to generated_files for final processing
                    if "data" in progress:
                        generated_files.append(progress["data"])
                elif progress["type"] == "error":
                    update_code_generation_session(
                        db, session_id,
                        {
                            "status": CodeGenerationStatus.FAILED,
                            "error_message": progress["message"],
                            "completed_at": "now"
                        }
                    )
                    return
                # When explanation is complete, trigger file operations but keep stream open
                if progress["type"] == "explanation_complete":
                    logger.info(f"Explanation complete for {session_id}, starting file operations...")

                    # Save generated files
                    logger.info(f"Saving {len(generated_files)} generated files - session {session_id}")
                    save_result = await file_management_service.save_generated_files(project.name, generated_files)

                    if save_result["success"]:
                        # Create zip file
                        logger.info(f"Creating zip file - session {session_id}")
                        zip_path = await file_management_service.create_project_zip(project.name)

                        if zip_path:
                            # Update session as completed
                            update_code_generation_session(
                                db, session_id,
                                {
                                    "status": CodeGenerationStatus.COMPLETED,
                                    "generated_files_count": len(generated_files),
                                    "zip_file_path": zip_path,
                                    "completed_at": "now"
                                }
                            )
                            logger.info(f"File operations completed for {session_id}, stream will close automatically")

                    # Stream will close automatically after explanation_complete event

        # File operations are now handled within the stream loop when explanation_complete is received
        # This code is unreachable since we don't break the stream loop anymore
        logger.info(f"Code generation background task completed for session {session_id}, stream continues for interactive chat")

    except Exception as e:
        logger.error(f"Error in code generation background task: {e}")
        update_code_generation_session(
            db, session_id,
            {
                "status": CodeGenerationStatus.FAILED,
                "error_message": str(e),
                "completed_at": "now"
            }
        )

        # Send error status to streaming store (only if not cancelled)
        try:
            if not streaming_store.is_session_cancelled(session_id):
                await streaming_store.add_progress(session_id, {
                    "type": "error",
                    "status": "failed",
                    "message": f"Code generation failed: {str(e)}"
                })
        except Exception as stream_error:
            logger.error(f"Failed to send error to stream: {stream_error}")
    finally:
        db.close()

def _analyze_repository_files(repo_files: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Analyze repository files and return statistics."""
    total_files = len(repo_files)
    code_files = 0
    test_files = 0
    config_files = 0
    documentation_files = 0

    detected_languages = set()
    detected_frameworks = set()

    for file_info in repo_files:
        file_path = file_info['path'].lower()

        # Count file types
        if any(ext in file_path for ext in ['.py', '.js', '.ts', '.java', '.cs', '.cpp', '.c']):
            code_files += 1

            # Detect languages
            if file_path.endswith('.py'):
                detected_languages.add('Python')
            elif file_path.endswith(('.js', '.ts')):
                detected_languages.add('JavaScript/TypeScript')
            elif file_path.endswith('.java'):
                detected_languages.add('Java')
            elif file_path.endswith('.cs'):
                detected_languages.add('C#')

        if 'test' in file_path:
            test_files += 1

        if any(name in file_path for name in ['config', 'settings', '.json', '.yaml', '.yml', '.xml']):
            config_files += 1

        if any(ext in file_path for ext in ['.md', '.txt', '.rst', 'readme', 'license']):
            documentation_files += 1

        # Detect frameworks (basic detection)
        content = file_info.get('content', '')
        if content:
            content_lower = content.lower()
            if 'selenium' in content_lower:
                detected_frameworks.add('Selenium')
            if 'playwright' in content_lower:
                detected_frameworks.add('Playwright')
            if 'pytest' in content_lower:
                detected_frameworks.add('Pytest')
            if 'junit' in content_lower:
                detected_frameworks.add('JUnit')

    return {
        "total_files": total_files,
        "code_files": code_files,
        "test_files": test_files,
        "config_files": config_files,
        "documentation_files": documentation_files,
        "detected_frameworks": list(detected_frameworks),
        "detected_languages": list(detected_languages),
        "recommendations": _generate_recommendations(list(detected_languages), list(detected_frameworks), test_files)
    }

def _generate_recommendations(languages: List[str], frameworks: List[str], test_files_count: int) -> List[str]:
    """Generate recommendations based on repository analysis."""
    recommendations = []

    if test_files_count == 0:
        recommendations.append("Consider adding automated tests to improve code quality")

    if not frameworks:
        recommendations.append("No automation frameworks detected. Consider adding Selenium or Playwright for web testing")

    if len(languages) > 1:
        recommendations.append("Multiple programming languages detected. Ensure consistent testing approach across languages")

    return recommendations
