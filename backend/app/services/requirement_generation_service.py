import httpx
import json
from typing import List, Optional, Dict, Any
from app.schemas.project import ImportedTestCase, ImportedRequirement
from app.core.config import settings

class RequirementGenerationService:
    def __init__(self):
        # Use Ollama instead of OpenAI
        self.ollama_url = getattr(settings, 'OLLAMA_URL', 'http://localhost:11434')
        self.model_name = getattr(settings, 'OLLAMA_MODEL', 'llama3.2:latest')
        # Initialize persistent HTTP client for connection reuse
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(60.0, connect=10.0),
            limits=httpx.Limits(max_keepalive_connections=5, max_connections=10)
        )
        self._closed = False
        
    async def generate_requirement_description(self, requirement_name: str,
                                                test_cases: List[ImportedTestCase]) -> str:
        """Generate a requirement description from test cases using Ollama LLM."""
        try:
            # Prepare the prompt
            prompt = self._create_prompt(requirement_name, test_cases)

            # Call Ollama API using persistent client
            if self._closed:
                print("RequirementGenerationService client is closed, cannot make request")
                return "Unable to generate requirement description - service unavailable"

            response = await self.client.post(
                f"{self.ollama_url}/api/generate",
                json={
                    "model": self.model_name,
                    "prompt": f"You are a business analyst. Write only the requirement description - no introductory text, no explanations, no questions. Just the requirement description itself.\n\n{prompt}",
                    "stream": False,
                    "options": {
                        "temperature": 0.3,
                        "num_predict": 500
                    }
                }
            )

            if response.status_code == 200:
                result = response.json()
                description = result.get('response', '').strip()
                if description:
                    # Clean up the response to remove filler text
                    cleaned_description = self._clean_llm_response(description)
                    return cleaned_description
                else:
                    print("Ollama returned empty response")
                    return self._generate_fallback_description(requirement_name, test_cases)
            else:
                print(f"Ollama API error: {response.status_code} - {response.text}")
                return self._generate_fallback_description(requirement_name, test_cases)

        except Exception as e:
            print(f"Error generating requirement description with Ollama: {str(e)}")
            return self._generate_fallback_description(requirement_name, test_cases)

    async def generate_requirement_descriptions_bulk(
        self,
        requirements_data: List[Dict[str, Any]]
    ) -> List[str]:
        """Generate multiple requirement descriptions concurrently for better performance."""
        import asyncio

        if not requirements_data:
            return []

        # Create tasks for concurrent execution
        tasks = []
        for req_data in requirements_data:
            task = self.generate_requirement_description(
                req_data["requirement_name"],
                req_data["test_cases"]
            )
            tasks.append(task)

        # Execute all generation tasks concurrently with semaphore control
        # The semaphore in the LLM service will handle concurrency limiting
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process results and handle exceptions
        descriptions = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                print(f"Failed to generate description for requirement {i}: {result}")
                # Use fallback for failed generations
                req_data = requirements_data[i]
                fallback = self._generate_fallback_description(
                    req_data["requirement_name"],
                    req_data["test_cases"]
                )
                descriptions.append(fallback)
            else:
                descriptions.append(result)

        return descriptions
    
    def _create_prompt(self, requirement_name: str, test_cases: List[ImportedTestCase]) -> str:
        """Create a prompt for LLM to generate requirement description."""
        prompt = f"""Requirement: {requirement_name}

Test Cases:
"""

        for i, test_case in enumerate(test_cases, 1):
            title = test_case.title or f"Test Case {i}"
            prompt += f"\n{i}. {title}\n"
            prompt += f"   Steps: {test_case.steps}\n"
            if test_case.expected_result:
                prompt += f"   Expected Result: {test_case.expected_result}\n"
            if test_case.notes:
                prompt += f"   Notes: {test_case.notes}\n"

        prompt += """
Write a professional requirement description (2-4 sentences) that:
- States what functionality needs to be implemented
- Uses business language from a user perspective
- Captures the key behaviors shown in the test cases
- Does NOT include introductory phrases, questions, or requests for feedback

Description:"""

        return prompt

    def _clean_llm_response(self, response: str) -> str:
        """Clean up LLM response to remove filler text and unwanted phrases."""
        import re

        # Remove common filler phrases at the beginning
        filler_patterns = [
            r'^Here is a clear and concise requirement description.*?:',
            r'^Based on the provided test cases.*?:',
            r'^The requirement description is.*?:',
            r'^Here\'s a professional requirement description.*?:',
            r'^Here is the requirement description.*?:',
            r'^The following is a requirement description.*?:',
        ]

        cleaned = response
        for pattern in filler_patterns:
            cleaned = re.sub(pattern, '', cleaned, flags=re.IGNORECASE | re.MULTILINE).strip()

        # Remove common filler phrases at the end
        end_filler_patterns = [
            r'Please let me know if you would like me to make any adjustments!?',
            r'Let me know if you need any modifications!?',
            r'Feel free to ask for any changes!?',
            r'I hope this helps!?',
            r'Is this what you were looking for\??',
        ]

        for pattern in end_filler_patterns:
            cleaned = re.sub(pattern, '', cleaned, flags=re.IGNORECASE | re.MULTILINE).strip()

        # Remove extra whitespace and newlines
        cleaned = re.sub(r'\n\s*\n', '\n', cleaned)  # Remove multiple newlines
        cleaned = re.sub(r'(^\s+)|(\s+$)', '', cleaned)  # Remove leading/trailing whitespace

        return cleaned if cleaned else response  # Return original if cleaning resulted in empty string

    def _generate_fallback_description(self, requirement_name: str,
                                     test_cases: List[ImportedTestCase]) -> str:
        """Generate a simple fallback description when LLM is not available."""
        test_count = len(test_cases)
        
        # Extract key actions from test cases
        actions = set()
        for test_case in test_cases:
            steps = test_case.steps.lower()
            if 'login' in steps:
                actions.add('login')
            if 'register' in steps or 'sign up' in steps:
                actions.add('registration')
            if 'password' in steps:
                actions.add('password management')
            if 'search' in steps:
                actions.add('search')
            if 'create' in steps:
                actions.add('creation')
            if 'update' in steps or 'edit' in steps:
                actions.add('updating')
            if 'delete' in steps:
                actions.add('deletion')
            if 'view' in steps or 'display' in steps:
                actions.add('viewing')
        
        if actions:
            action_list = ', '.join(sorted(actions))
            description = f"This requirement covers {action_list} functionality. "
        else:
            description = f"This requirement covers the functionality described in {requirement_name}. "
        
        description += f"It includes {test_count} test case{'s' if test_count != 1 else ''} that validate the expected behavior and ensure proper implementation of the feature."

    async def close(self):
        """Close the persistent HTTP client."""
        if not self._closed and self.client:
            await self.client.aclose()
            self._closed = True
            print("RequirementGenerationService HTTP client closed")

    async def __aenter__(self):
        """Async context manager entry."""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.close()
    


# Global instance
requirement_generation_service = RequirementGenerationService()
