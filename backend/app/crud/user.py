from typing import Optional, List
from sqlalchemy.orm import Session
from sqlalchemy import or_, func
from app.core.security import get_password_hash, verify_password
from app.models.user import User
from app.schemas.user import UserCreate, UserUpdate

def get_user(db: Session, user_id: int) -> Optional[User]:
    return db.query(User).filter(User.id == user_id).first()

def get_user_by_email(db: Session, email: str) -> Optional[User]:
    return db.query(User).filter(User.email == email).first()

def create_user(db: Session, user: UserCreate) -> User:
    # Handle OAuth users without passwords
    hashed_password = get_password_hash(user.password) if user.password else ""
    db_user = User(
        email=user.email,
        hashed_password=hashed_password,
        full_name=user.full_name,
        is_active=user.is_active,
        auth_provider=user.auth_provider,
        profile_image_url=user.profile_image_url,
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

def authenticate_user(db: Session, email: str, password: str) -> Optional[User]:
    user = get_user_by_email(db, email)
    if not user:
        return None
    if not verify_password(password, user.hashed_password):
        return None
    return user

def update_user(db: Session, user_id: int, user_update: UserUpdate) -> Optional[User]:
    user = get_user(db, user_id)
    if not user:
        return None
    
    update_data = user_update.dict(exclude_unset=True)
    if "password" in update_data:
        update_data["hashed_password"] = get_password_hash(update_data.pop("password"))
    
    for field, value in update_data.items():
        setattr(user, field, value)
    
    db.commit()
    db.refresh(user)
    return user

def search_users(db: Session, query: str, limit: int = 20) -> List[User]:
    """Search users by email or full name with optimized query."""
    if not query.strip():
        return []

    search_term = f"%{query.lower()}%"
    return (
        db.query(User)
        .filter(
            User.is_active == True,  # Filter active users first (indexed)
            or_(
                func.lower(User.email).like(search_term),  # Use LIKE instead of contains for better index usage
                func.lower(User.full_name).like(search_term)
            )
        )
        .order_by(User.email)  # Add consistent ordering
        .limit(limit)
        .all()
    )

def get_users(db: Session, skip: int = 0, limit: int = 2000) -> List[User]:
    """Get all active users."""
    return (
        db.query(User)
        .filter(User.is_active == True)
        .offset(skip)
        .limit(limit)
        .all()
    )
