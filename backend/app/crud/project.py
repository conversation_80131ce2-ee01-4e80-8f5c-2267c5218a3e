from typing import List, Optional
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func
from app.models.project import Project
from app.models.project_member import ProjectMember, MemberRole
from app.models.requirement import Requirement
from app.models.project_variable import ProjectVariable
from app.models.test_case import TestCase
from app.models.tag import RequirementTag
from app.schemas.project import ProjectCreate, ProjectUpdate
from app.services.rag_service import rag_service
from app.utils.slug import generate_project_slug

def get_project(db: Session, project_id: int) -> Optional[Project]:
    project = db.query(Project).filter(Project.id == project_id).first()
    if project:
        # Populate user details for members
        from app.models.user import User
        for member in project.members:
            user = db.query(User).filter(User.id == member.user_id).first()
            if user:
                member.user_email = user.email
                member.user_name = user.full_name
    return project


def get_project_by_slug(db: Session, slug: str) -> Optional[Project]:
    from app.models.user import User
    # Use joinedload to avoid N+1 queries
    project = (
        db.query(Project)
        .options(
            joinedload(Project.members).joinedload(ProjectMember.user)
        )
        .filter(Project.slug == slug)
        .first()
    )
    if project:
        # Populate user details for members (data already loaded via joinedload)
        for member in project.members:
            if member.user:
                member.user_email = member.user.email
                member.user_name = member.user.full_name
    return project

def get_projects_for_user(db: Session, user_id: int, skip: int = 0, limit: int = 2000) -> List[Project]:
    return (
        db.query(Project)
        .join(ProjectMember)
        .filter(ProjectMember.user_id == user_id)
        .offset(skip)
        .limit(limit)
        .all()
    )

def get_projects_with_stats(db: Session, user_id: int, skip: int = 0, limit: int = 2000):
    """Get projects with member, requirement, and test case counts, ordered by creation date (newest first)."""
    # Subquery to get member count for each project
    member_count_subquery = (
        db.query(
            ProjectMember.project_id,
            func.count(ProjectMember.id).label("member_count")
        )
        .group_by(ProjectMember.project_id)
        .subquery()
    )

    # Subquery to get requirement count for each project
    requirement_count_subquery = (
        db.query(
            Requirement.project_id,
            func.count(Requirement.id).label("requirement_count")
        )
        .group_by(Requirement.project_id)
        .subquery()
    )

    # Subquery to get test case count for each project
    test_case_count_subquery = (
        db.query(
            Requirement.project_id,
            func.count(TestCase.id).label("test_case_count")
        )
        .join(TestCase, Requirement.id == TestCase.requirement_id)
        .group_by(Requirement.project_id)
        .subquery()
    )

    return (
        db.query(
            Project,
            func.coalesce(member_count_subquery.c.member_count, 0).label("member_count"),
            func.coalesce(requirement_count_subquery.c.requirement_count, 0).label("requirement_count"),
            func.coalesce(test_case_count_subquery.c.test_case_count, 0).label("test_case_count")
        )
        .join(ProjectMember, Project.id == ProjectMember.project_id)
        .outerjoin(member_count_subquery, Project.id == member_count_subquery.c.project_id)
        .outerjoin(requirement_count_subquery, Project.id == requirement_count_subquery.c.project_id)
        .outerjoin(test_case_count_subquery, Project.id == test_case_count_subquery.c.project_id)
        .filter(ProjectMember.user_id == user_id)
        .group_by(Project.id, member_count_subquery.c.member_count, requirement_count_subquery.c.requirement_count, test_case_count_subquery.c.test_case_count)
        .order_by(Project.created_at.desc())
        .offset(skip)
        .limit(limit)
        .all()
    )

def create_project(db: Session, project: ProjectCreate, owner_id: int) -> Project:
    # Generate unique slug
    slug = generate_project_slug(db, project.name)

    db_project = Project(
        **project.model_dump(),
        slug=slug
    )
    db.add(db_project)
    db.commit()
    db.refresh(db_project)

    # Add the creator as owner
    db_member = ProjectMember(
        project_id=db_project.id,
        user_id=owner_id,
        role=MemberRole.OWNER
    )
    db.add(db_member)
    db.commit()

    # Refresh the project to get the updated members and populate user details
    db.refresh(db_project)
    return get_project(db, db_project.id)

def update_project(db: Session, project_id: int, project_update: ProjectUpdate) -> Optional[Project]:
    project = get_project(db, project_id)
    if not project:
        return None
    
    update_data = project_update.model_dump(exclude_unset=True)

    for field, value in update_data.items():
        # Handle enum fields specially
        if field in ["automation_framework", "programming_language"]:
            # Get the original value from the Pydantic model (could be None or enum)
            enum_value = getattr(project_update, field)
            setattr(project, field, enum_value)
        else:
            setattr(project, field, value)
    
    db.commit()
    db.refresh(project)

    # Return the project with populated user details
    return get_project(db, project_id)

def delete_project(db: Session, project_id: int) -> bool:
    project = get_project(db, project_id)
    if not project:
        return False

    try:
        # Get all requirements for this project to delete from vector database
        requirements = db.query(Requirement).filter(Requirement.project_id == project_id).all()

        # Collect all tag IDs used by requirements in this project for cleanup
        all_tag_ids = set()
        for requirement in requirements:
            for requirement_tag in requirement.requirement_tags:
                all_tag_ids.add(requirement_tag.tag_id)

        # Delete from vector database first
        for requirement in requirements:
            try:
                rag_service.delete_requirement(requirement.id)
            except Exception:
                # Continue even if vector deletion fails
                pass

        # Delete code generation related data first (to avoid foreign key constraints)
        from app.models.code_generation import (
            CodeGenerationSession, GeneratedFile, CodeGenerationLog
        )

        # Get all code generation sessions for this project
        sessions = db.query(CodeGenerationSession).filter(CodeGenerationSession.project_id == project_id).all()
        session_ids = [session.id for session in sessions]

        if session_ids:
            # Delete generated files first
            db.query(GeneratedFile).filter(GeneratedFile.session_id.in_(session_ids)).delete(synchronize_session=False)

            # Delete code generation logs
            db.query(CodeGenerationLog).filter(CodeGenerationLog.session_id.in_(session_ids)).delete(synchronize_session=False)

        # Delete code generation sessions
        db.query(CodeGenerationSession).filter(CodeGenerationSession.project_id == project_id).delete(synchronize_session=False)

        # Delete related records in the correct order to avoid foreign key constraints
        # Delete test cases first
        db.query(TestCase).filter(
            TestCase.requirement_id.in_(
                db.query(Requirement.id).filter(Requirement.project_id == project_id)
            )
        ).delete(synchronize_session=False)

        # Delete requirement tags
        db.query(RequirementTag).filter(
            RequirementTag.requirement_id.in_(
                db.query(Requirement.id).filter(Requirement.project_id == project_id)
            )
        ).delete(synchronize_session=False)

        # Delete requirements
        db.query(Requirement).filter(Requirement.project_id == project_id).delete()

        # Delete project variables
        db.query(ProjectVariable).filter(ProjectVariable.project_id == project_id).delete()

        # Delete project members
        db.query(ProjectMember).filter(ProjectMember.project_id == project_id).delete(synchronize_session=False)

        # Finally delete the project
        db.delete(project)

        # Clean up orphaned tags
        from app.crud.requirement import _cleanup_orphaned_tags
        _cleanup_orphaned_tags(db, list(all_tag_ids))

        db.commit()
        return True
    except Exception as e:
        db.rollback()
        # Log the error for debugging
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error deleting project {project_id}: {str(e)}")
        raise e

def get_user_role_in_project(db: Session, project_id: int, user_id: int) -> Optional[MemberRole]:
    member = (
        db.query(ProjectMember)
        .filter(ProjectMember.project_id == project_id, ProjectMember.user_id == user_id)
        .first()
    )
    return member.role if member else None

def is_project_member(db: Session, project_id: int, user_id: int) -> bool:
    return (
        db.query(ProjectMember)
        .filter(ProjectMember.project_id == project_id, ProjectMember.user_id == user_id)
        .first()
    ) is not None

def get_project_member(db: Session, member_id: int) -> Optional[ProjectMember]:
    """Get a specific project member by ID."""
    return db.query(ProjectMember).filter(ProjectMember.id == member_id).first()

def get_project_members(db: Session, project_id: int) -> List[ProjectMember]:
    """Get all members of a project with user details."""
    from app.models.user import User
    return (
        db.query(ProjectMember)
        .join(User, ProjectMember.user_id == User.id)
        .filter(ProjectMember.project_id == project_id)
        .all()
    )

def add_project_member(db: Session, project_id: int, user_id: int, role: MemberRole = MemberRole.MEMBER) -> ProjectMember:
    """Add a new member to a project."""
    # Check if user is already a member
    existing_member = (
        db.query(ProjectMember)
        .filter(ProjectMember.project_id == project_id, ProjectMember.user_id == user_id)
        .first()
    )
    if existing_member:
        raise ValueError("User is already a member of this project")

    db_member = ProjectMember(
        project_id=project_id,
        user_id=user_id,
        role=role
    )
    db.add(db_member)
    db.commit()
    db.refresh(db_member)

    # Populate user details
    from app.models.user import User
    user = db.query(User).filter(User.id == user_id).first()
    if user:
        db_member.user_email = user.email
        db_member.user_name = user.full_name

    return db_member

def update_project_member_role(db: Session, project_id: int, member_id: int, role: MemberRole) -> Optional[ProjectMember]:
    """Update a project member's role."""
    member = (
        db.query(ProjectMember)
        .filter(ProjectMember.id == member_id, ProjectMember.project_id == project_id)
        .first()
    )
    if not member:
        return None

    # Note: Owner role changes are now allowed
    # Permission checking is handled at the API level

    member.role = role
    db.commit()
    db.refresh(member)

    # Populate user details
    from app.models.user import User
    user = db.query(User).filter(User.id == member.user_id).first()
    if user:
        member.user_email = user.email
        member.user_name = user.full_name

    return member

def remove_project_member(db: Session, project_id: int, member_id: int) -> bool:
    """Remove a member from a project."""
    member = (
        db.query(ProjectMember)
        .filter(ProjectMember.id == member_id, ProjectMember.project_id == project_id)
        .first()
    )
    if not member:
        return False

    # Note: Owner removal is now allowed (owners can remove themselves)
    # Permission checking is handled at the API level

    db.delete(member)
    db.commit()
    return True
