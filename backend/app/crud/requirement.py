from typing import List, Optional
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func
from app.models.requirement import Requirement
from app.models.test_case import TestCase
from app.models.tag import Tag, RequirementTag
from app.models.user import User
from app.schemas.requirement import RequirementCreate, RequirementUpdate
from app.services.rag_service import rag_service
from app.utils.slug import generate_requirement_slug

def get_requirement(db: Session, requirement_id: int) -> Optional[Requirement]:
    return db.query(Requirement).options(joinedload(Requirement.tags)).filter(Requirement.id == requirement_id).first()


def get_requirement_by_slug(db: Session, project_id: int, slug: str) -> Optional[Requirement]:
    return db.query(Requirement).options(joinedload(Requirement.tags)).filter(
        Requirement.project_id == project_id,
        Requirement.slug == slug
    ).first()

def get_requirements_by_project_id(db: Session, project_id: int):
    """Get all requirements for a project by project ID."""
    return (
        db.query(Requirement)
        .filter(Requirement.project_id == project_id)
        .options(joinedload(Requirement.tags))
        .all()
    )

def get_requirements_by_project_name(db: Session, project_name: str):
    """Get all requirements for a project by project name."""
    from app.models.project import Project
    return (
        db.query(Requirement)
        .join(Project, Requirement.project_id == Project.id)
        .filter(Project.name == project_name)
        .all()
    )

def get_requirements_for_project(db: Session, project_id: int, skip: int = 0, limit: int = 2000):
    """Get requirements for a project with test case counts and user information, ordered by creation date (newest first)."""
    return (
        db.query(
            Requirement,
            func.count(TestCase.id).label("test_case_count"),
            User.full_name.label("created_by_name")
        )
        .options(joinedload(Requirement.tags))
        .join(User, Requirement.created_by == User.id)
        .outerjoin(TestCase, Requirement.id == TestCase.requirement_id)
        .filter(Requirement.project_id == project_id)
        .group_by(Requirement.id, User.full_name)
        .order_by(Requirement.created_at.desc())
        .offset(skip)
        .limit(limit)
        .all()
    )

def create_requirement(db: Session, requirement: RequirementCreate, created_by: int) -> Requirement:
    # Generate unique slug within the project
    slug = generate_requirement_slug(db, requirement.name, requirement.project_id)

    db_requirement = Requirement(
        name=requirement.name,
        slug=slug,
        description=requirement.description,
        refined_description=requirement.refined_description,
        project_id=requirement.project_id,
        created_by=created_by
    )
    db.add(db_requirement)
    db.commit()
    db.refresh(db_requirement)

    # Add tags
    if requirement.tag_names:
        for tag_name in requirement.tag_names:
            tag = get_or_create_tag(db, tag_name)
            db_requirement_tag = RequirementTag(
                requirement_id=db_requirement.id,
                tag_id=tag.id
            )
            db.add(db_requirement_tag)
        db.commit()

    # Add to vector database
    rag_service.add_requirement(
        requirement_id=db_requirement.id,
        project_id=db_requirement.project_id,
        text=db_requirement.description,
        metadata={
            "name": db_requirement.name,
            "created_by": db_requirement.created_by,
            "tags": requirement.tag_names or []
        }
    )

    # Refresh the requirement with tags loaded
    db.refresh(db_requirement)
    db_requirement = db.query(Requirement).options(joinedload(Requirement.tags)).filter(Requirement.id == db_requirement.id).first()

    return db_requirement

def create_requirements_bulk(db: Session, requirements: List[RequirementCreate], created_by: int) -> List[Requirement]:
    """Create multiple requirements in bulk for better performance."""
    db_requirements = []

    # First, create all requirements without committing
    for requirement in requirements:
        # Generate unique slug within the project
        slug = generate_requirement_slug(db, requirement.name, requirement.project_id)

        db_requirement = Requirement(
            name=requirement.name,
            slug=slug,
            description=requirement.description,
            refined_description=requirement.refined_description,
            project_id=requirement.project_id,
            created_by=created_by
        )
        db.add(db_requirement)
        db_requirements.append((db_requirement, requirement.tag_names or []))

    # Commit all requirements at once
    db.commit()

    # Refresh all requirements to get their IDs
    for db_requirement, _ in db_requirements:
        db.refresh(db_requirement)

    # Add tags in bulk
    requirement_tags = []
    for db_requirement, tag_names in db_requirements:
        for tag_name in tag_names:
            tag = get_or_create_tag(db, tag_name)
            requirement_tags.append(RequirementTag(
                requirement_id=db_requirement.id,
                tag_id=tag.id
            ))

    if requirement_tags:
        db.add_all(requirement_tags)
        db.commit()

    # Add to vector database in bulk
    for db_requirement, tag_names in db_requirements:
        rag_service.add_requirement(
            requirement_id=db_requirement.id,
            project_id=db_requirement.project_id,
            text=db_requirement.description,
            metadata={
                "name": db_requirement.name,
                "created_by": db_requirement.created_by,
                "tags": tag_names
            }
        )

    # Return just the requirement objects
    return [db_requirement for db_requirement, _ in db_requirements]

def update_requirement(db: Session, requirement_id: int, requirement_update: RequirementUpdate) -> Optional[Requirement]:
    requirement = get_requirement(db, requirement_id)
    if not requirement:
        return None
    
    update_data = requirement_update.dict(exclude_unset=True, exclude={"tag_names"})
    for field, value in update_data.items():
        setattr(requirement, field, value)
    
    # Update tags if provided
    if requirement_update.tag_names is not None:
        # Remove existing tags
        db.query(RequirementTag).filter(RequirementTag.requirement_id == requirement_id).delete()
        
        # Add new tags
        for tag_name in requirement_update.tag_names:
            tag = get_or_create_tag(db, tag_name)
            db_requirement_tag = RequirementTag(
                requirement_id=requirement_id,
                tag_id=tag.id
            )
            db.add(db_requirement_tag)
    
    db.commit()
    db.refresh(requirement)

    # Update in vector database
    rag_service.update_requirement(
        requirement_id=requirement.id,
        text=requirement.description,
        metadata={
            "project_id": requirement.project_id,
            "name": requirement.name,
            "created_by": requirement.created_by,
            "tags": requirement_update.tag_names or []
        }
    )

    return requirement

def delete_requirement(db: Session, requirement_id: int) -> bool:
    requirement = get_requirement(db, requirement_id)
    if not requirement:
        return False

    try:
        # Get tags associated with this requirement before deletion for cleanup
        requirement_tag_ids = [rt.tag_id for rt in requirement.requirement_tags]

        # Delete from vector database first
        rag_service.delete_requirement(requirement_id)

        # Import here to avoid circular imports
        from app.models.code_generation import CodeGenerationSession

        # Delete related code generation sessions first
        db.query(CodeGenerationSession).filter(CodeGenerationSession.requirement_id == requirement_id).delete()

        # Delete related requirement_tags first to avoid foreign key constraint violation
        db.query(RequirementTag).filter(RequirementTag.requirement_id == requirement_id).delete()

        # Delete related test_cases
        db.query(TestCase).filter(TestCase.requirement_id == requirement_id).delete()

        # Now delete the requirement
        db.delete(requirement)

        # Clean up orphaned tags
        _cleanup_orphaned_tags(db, requirement_tag_ids)

        db.commit()
        return True
    except Exception as e:
        db.rollback()
        # Log the error for debugging
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error deleting requirement {requirement_id}: {str(e)}")
        raise e

def get_or_create_tag(db: Session, tag_name: str) -> Tag:
    tag = db.query(Tag).filter(Tag.name == tag_name).first()
    if not tag:
        tag = Tag(name=tag_name)
        db.add(tag)
        db.commit()
        db.refresh(tag)
    return tag

def get_tags(db: Session, skip: int = 0, limit: int = 2000) -> List[Tag]:
    return db.query(Tag).offset(skip).limit(limit).all()

def _cleanup_orphaned_tags(db: Session, tag_ids: List[int]) -> None:
    """Clean up tags that are no longer referenced by any requirements."""
    for tag_id in tag_ids:
        # Check if this tag is still referenced by any requirement
        remaining_references = db.query(RequirementTag).filter(RequirementTag.tag_id == tag_id).count()

        if remaining_references == 0:
            # Tag is orphaned, delete it
            tag = db.query(Tag).filter(Tag.id == tag_id).first()
            if tag:
                db.delete(tag)
                import logging
                logger = logging.getLogger(__name__)
                logger.debug(f"Deleted orphaned tag: {tag.name} (ID: {tag_id})")
