{"name": "playwright-automation", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"test": "jest", "e2e": "playwright test --color=never"}, "keywords": [], "author": "", "license": "MIT", "dependencies": {"@types/jest": "^27.0.1", "@types/mocha": "^10.20.14", "@types/playwright": "^1.29.4", "@playwright/test": "^1.28.2", "assert": "^3.3.0", "chai": "^4.3.5", "config": "^2.5.0", "jest": "^27.0.6", "jest-environment-jsdom": "^27.0.1", "mocha": "^10.20.14", "npm-lifecycle-logs": "^2.4.1", "playwright": "^1.28.2", "recast": "^0.21.5", "reflect-metadata": "^0.2.7"}, "devDependencies": {"@types/node": "^17.0.56", "@types/react": "^17.0.55", "@types/react-dom": "^17.0.54", "babel-jest": "^27.0.6", "babel-loader": "^8.2.2", "jest-config": "^4.9.5", "jest-preset-env": "^26.0.0", "typescript": "^4.7.4"}, "peerDependencies": {"@types/node": "^17.0.56"}, "private": true}