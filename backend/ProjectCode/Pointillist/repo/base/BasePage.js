// page-object.js

const { Client } = require('playwright');
const logger = console;

class BasePage {
  constructor(client) {
    this.client = client;
    this.browser = client Brower();
    this.context = this.browser.createContext();
    this.page = this.context.getPage();
  }

  async navigate(url) {
    await this.page.goto(url);
    return new Promise((resolve, reject) => {
      logger.info('Navigated to:', url);
      resolve(this);
    });
  }

  async waitForElement(selector) {
    const waitStrategy = 'visibility';
    const timeout = 10000;

    try {
      const element = await this.page.$(selector, { waitUntil: [waitStrategy] });
      return new Promise((resolve) => {
        logger.info('Element:', selector);
        resolve(element);
      });
    } catch (error) {
      throw new Error(`Element not found: ${selector}`);
    }
  }

  async waitForElementVisible(selector) {
    const waitStrategy = 'visibility';
    const timeout = 10000;

    try {
      const element = await this.page.$(selector, { waitUntil: [waitStrategy] });
      return new Promise((resolve) => {
        logger.info('Element:', selector);
        resolve(element);
      });
    } catch (error) {
      throw new Error(`Element not found: ${selector}`);
    }
  }

  async waitForElementText(selector, text) {
    const waitStrategy = 'text';
    const timeout = 10000;

    try {
      await this.waitForElement(selector);
      return new Promise((resolve) => {
        logger.info('Element:', selector);
        resolve(await this.page.$eval(selector, (el) => el.textContent.trim()));
      });
    } catch (error) {
      throw new Error(`Element not found: ${selector}`);
    }
  }

  async clickElement(selector) {
    try {
      await this.waitForElement(selector);
      return new Promise((resolve) => {
        logger.info('Clicked:', selector);
        resolve(await this.page.click(selector));
      });
    } catch (error) {
      throw new Error(`Element not found: ${selector}`);
    }
  }

  async enterText(selector, text) {
    try {
      await this.waitForElement(selector);
      return new Promise((resolve) => {
        logger.info('Entered:', selector);
        resolve(await this.page.type(selector, text));
      });
    } catch (error) {
      throw new Error(`Element not found: ${selector}`);
    }
  }

  async getInnerText(selector) {
    try {
      await this.waitForElement(selector);
      return new Promise((resolve) => {
        logger.info('Got innerText:', selector);
        resolve(await this.page.$eval(selector, (el) => el.textContent.trim()));
      });
    } catch (error) {
      throw new Error(`Element not found: ${selector}`);
    }
  }

  async getOuterHtml(selector) {
    try {
      await this.waitForElement(selector);
      return new Promise((resolve) => {
        logger.info('Got outerHTML:', selector);
        resolve(await this.page.$eval(selector, (el) => el.outerHTML));
      });
    } catch (error) {
      throw new Error(`Element not found: ${selector}`);
    }
  }

  async close() {
    return new Promise((resolve) => {
      logger.info('Closed browser');
      resolve();
    }).then(() => this.client.close());
  }
}

module.exports = BasePage;