// Import required dependencies
const { test, expect } = require('@playwright/test');
const { browserConfig } = require('./browser-config');
const { waitUtilities } = require('./wait-utils');

class TestBase {
  async setup() {
    this.browser = await browserConfig.setupBrowser();
    this.page = await this.browser.newPage();
  }

  async teardown() {
    await this.browser.close();
  }

  async takeScreenshotOnFailure(page, path) {
    try {
      const screenshot = await page.screenshot({ path });
      console.log(`Screenshot saved to ${path}`);
    } catch (error) {
      throw error;
    }
  }

  async waitForElement(elementSelector) {
    return waitUtilities.waitForElement(this.page, elementSelector);
  }

  async clickOnElement(elementSelector) {
    const element = await this(page). locator(`[data-test=${elementSelector}]`);
    return await element.click();
  }

  async enterText(elementSelector, text) {
    const element = await this(page).locator(`[data-test=${elementSelector}]`);
    return await element.type(text);
  }

  async verifyText(elementSelector, expectedText) {
    const element = await this(page).locator(`[data-test=${elementSelector}]`);
    return await expect(element.text()).toBe(expectedText);
  }
}

module.exports = TestBase;

// browser-config.js
const { launch } = require('@playwright/test');

module.exports.setupBrowser = async () => {
  const browser = await launch();
  return browser;
};

module.exports.browserConfig = {
  // Add your browser configuration here
};

// wait-utils.js
const { waitFor } = require('@playwright/test');
const { setTimeout } = require('timers');

module.exports.waitForElement = async (page, elementSelector) => {
  const timeout = 5000; // 5 seconds
  const maxRetries = 3;
  let retryCount = 0;

  while (retryCount < maxRetries && !page.locator(elementSelector).isVisiable()) {
    await waitFor(page, elementSelector);
    if (retryCount >= maxRetries) {
      throw new Error(`Element not found after ${maxRetries} retries`);
    }
    retryCount++;
  }

  return page.locator(elementSelector).isVisiable();
};

// test.js
const TestBase = require('./test-base');
const { test, expect } = require('@playwright/test');

test('My First Playwright Test', async ({ page }) => {
  await TestBase.setup();
  // Add your test code here
});