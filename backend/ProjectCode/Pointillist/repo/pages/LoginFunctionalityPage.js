// loginPage.js
const BasePage = require('./basePage');
const { element, by } = require('playwright');

class LoginPage extends BasePage {
  get usernameInput() {
    return element(by.id('username'));
  }

  get passwordInput() {
    return element(by.id('password'));
  }

  get loginButton() {
    return element(by.xpath('//button[@type="submit"]'));
  }

  get errorMessageElement() {
    return element(by.css('.error-message'));
  }

  async setUsername(username) {
    try {
      await this.usernameInput.type(username);
    } catch (e) {
      console.error(`Failed to set username: ${e.message}`);
    }
  }

  async setPassword(password) {
    try {
      await this.passwordInput.type(password);
    } catch (e) {
      console.error(`Failed to set password: ${e.message}`);
    }
  }

  async clickLoginButton() {
    try {
      await this.loginButton.click();
    } catch (e) {
      console.error(`Failed to login button click: ${e.message}`);
    }
  }

  async validateErrorMessage(message) {
    try {
      const errorMessage = await this.errorMessageElement.textContent();
      if (errorMessage !== message) {
        throw new Error(`Invalid error message: ${errorMessage}`);
      }
    } catch (e) {
      console.error(`Failed to validate error message: ${e.message}`);
    }
  }

  async login(username, password) {
    try {
      await this.setUsername(username);
      await this.setPassword(password);
      await this.clickLoginButton();
      return true;
    } catch (e) {
      console.error(`Login failed: ${e.message}`);
      return false;
    }
  }

  async validateLoginFailure() {
    try {
      const errorMessage = await this.errorMessageElement.textContent();
      if (!errorMessage.includes('Invalid username or password')) {
        throw new Error('Invalid login failure message');
      }
    } catch (e) {
      console.error(`Failed to validate login failure: ${e.message}`);
    }
  }
}

module.exports = LoginPage;

// basePage.js
class BasePage {
  async navigateTo(url) {
    try {
      await this.driver navigate(url);
    } catch (e) {
      console.error(`Navigation failed: ${e.message}`);
    }
  }

  async waitForElement(element) {
    try {
      await element.waitFor();
    } catch (e) {
      console.error(`Element not found: ${e.message}`);
    }
  }
}

module.exports = BasePage;