// Import required modules and dependencies
const { test, expect } = require('@playwright/test');
const { LoginPage, ProfilePage } from './page-objects';

// Define the test class
class LoginTest extends test {
  // Set up the test page objects
  async setup(page) {
    this.page = page;
    await page.goto('http://localhost:3000/login');
  }

  // Test Case 1: Login with valid credentials
  async testCase1() {
    const loginPage = new LoginPage(this.page);
    const profilePage = new ProfilePage(this.page);

    await loginPage.enterValidEmail();
    await loginPage.enterValidPassword();
    await loginPage.clickLoginButton();

    expect(await profilePage.isProfilePageLoaded()).toBe(true);
    await profilePage.checkAccountInformation();
  }

  // Test Case 2: Invalid email address
  async testCase2() {
    const loginPage = new LoginPage(this.page);

    await loginPage.enterInvalidEmail();
    await loginPage.clickLoginButton();

    expect(await loginPage.getErrorMessage()).toContain('Invalid Email Address');
  }

  // Test Case 3: Empty email address
  async testCase3() {
    const loginPage = new LoginPage(this.page);

    await loginPage.enterEmptyEmail();
    await loginPage.clickLoginButton();

    expect(await loginPage.getErrorMessage()).toContain('Email Address is required');
  }

  // Test Case 4: Valid email but missing password
  async testCase4() {
    const loginPage = new LoginPage(this.page);

    await loginPage.enterValidEmail();
    await loginPage.enterEmptyPassword();
    await loginPage.clickLoginButton();

    expect(await loginPage.getErrorMessage()).toContain('Password is required');
  }

  // Test Case 5: Password too short
  async testCase5() {
    const loginPage = new LoginPage(this.page);

    await loginPage.enterValidEmail();
    await loginPage.enterShortPassword();
    await loginPage.clickLoginButton();

    expect(await loginPage.getErrorMessage()).toContain('Password is too short');
  }

  // Test Case 6: Password contains only special characters
  async testCase6() {
    const loginPage = new LoginPage(this.page);

    await loginPage.enterValidEmail();
    await loginPage.enterSpecialCharPassword();
    await loginPage.clickLoginButton();

    expect(await loginPage.getErrorMessage()).toContain('Invalid password');
  }

  // Test Case 7: Login with uppercase and lowercase letters, digits
  async testCase7() {
    const loginPage = new LoginPage(this.page);

    await loginPage.enterValidEmail();
    await loginPage.enterStrongPassword();
    await loginPage.clickLoginButton();

    expect(await profilePage.isProfilePageLoaded()).toBe(true);
    await profilePage.checkAccountInformation();
  }

  // Test Case 8: Non-standard email address
  async testCase8() {
    const loginPage = new LoginPage(this.page);

    await loginPage.enterNonStandardEmail();
    await loginPage.clickLoginButton();

    expect(await loginPage.getErrorMessage()).toContain('Invalid Email Address');
  }

  // Test Case 9: Login with missing username
  async testCase9() {
    const loginPage = new LoginPage(this.page);

    await loginPage.enterEmptyEmail();
    await loginPage.enterEmptyPassword();
    await loginPage.clickLoginButton();

    expect(await loginPage.getErrorMessage()).toContain('Both Email Address and Password are required');
  }

  // Test Case 10: Login with valid credentials but no permission
  async testCase10() {
    const loginPage = new LoginPage(this.page);

    await loginPage.enterValidEmail();
    await loginPage.enterValidPassword();
    await loginPage.clickLoginButton();

    expect(await profilePage.isProfilePageLoaded()).toBe(false);
  }
}

// Run the test class
test('Login Test', async ({ page }) => {
  const loginTest = new LoginTest(page);
  await loginTest.setup();
  await loginTest.testCase1();
  await loginTest.testCase2();
  await loginTest.testCase3();
  await loginTest.testCase4();
  await loginTest.testCase5();
  await loginTest.testCase6();
  await loginTest.testCase7();
  await loginTest.testCase8();
  await loginTest.testCase9();
  await loginTest.testCase10();
});