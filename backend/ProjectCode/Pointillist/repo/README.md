# InfoNet Web Automation Project

## Table of Contents

1. [Overview](#overview)
2. [Prerequisites and Setup Instructions](#prerequisites-and-setup-instructions)
3. [How to Run Tests](#how-to-run-tests)
4. [Project Structure Explanation](#project-structure-explanation)
5. [Configuration Details](#configuration-details)
6. [Reporting Information](#reporting-information)
7. [Troubleshooting Tips](#troubleshooting-tips)

## Overview

InfoNet Web is a playwright automation project designed to automate interactions with web applications using JavaScript. This project aims to simplify the testing and validation of web-based systems, reducing the need for manual intervention.

## Prerequisites and Setup Instructions

### Dependencies

- Node.js (>= 14.17.0)
- npm (>= 6.14.13)
- playwright (>= 1.9.23)

### Installation

```bash
npm install

or

```bash
yarn install

### Setup Configuration

Create a `config.json` file in the root directory with the following structure:
{
  "browser": "chromium",
  "timeout": 30000,
  "useLocalFiles": true
}
Adjust the configuration settings according to your needs.

## How to Run Tests

### Running Tests

```bash
npm run test:web

or

```bash
yarn test:web

This command will execute all web tests in the project using the specified browser and configuration.

### Running Specific Test

To run a specific test, use the following command:
```bash
npx playwright test <test-name>
Replace `<test-name>` with the actual name of the test you want to run.

## Project Structure Explanation

The project structure is organized into the following directories:

- `src`: Source code for all tests and utilities.
- `tests`: Directory for test files, including web tests and helper functions.
- `config.json`: Configuration file for playwright automation.
- `.env`: Environment variables file (optional).

## Configuration Details

The configuration file (`config.json`) contains the following settings:

- `browser`: The browser to use for the automation. Can be set to "chromium", "firefox", or "webkit".
- `timeout`: The maximum time in milliseconds to wait for a test to complete.
- `useLocalFiles`: Whether to use local files instead of fetching them from an API.

## Reporting Information

Test results will be logged to the console and stored in the `test-report.json` file. You can customize the reporting format by modifying the `reporter` configuration in the `config.json` file.

## Troubleshooting Tips

- If a test fails, check the console output for error messages.
- Use the `npx playwright debug` command to enable developer tools and inspect test failures.
- Check the `test-report.json` file for detailed test results.