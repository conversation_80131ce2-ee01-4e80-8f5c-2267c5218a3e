// config.js

const path = require('path');
const { browser } = require('@playwright/browser');

module.exports = {
  // Browser Settings
  testFolder: './tests',
  // browsers: ['chromium', 'firefox', 'safari'],
  browsers: [
    {
      name: 'chromium',
      launchUrl: 'https://example.com',
      headless: false,
      ignoreDefaultArgs: ['--window-size=1920,1080']
    },
    {
      name: 'firefox',
      launchUrl: 'https://example.com',
      headless: true
    },
    {
      name: 'safari',
      launchUrl: 'https://example.com',
      headless: false,
      ignoreDefaultArgs: ['--window-size=1920,1080']
    }
  ],

  // Environment Configurations
  env: {
    CHROME_BROWSER: 'chromium',
    FIREFOX_BROWSER: 'firefox',
    SAFARI_BROWSER: 'safari'
  },

  // Timeout Settings
  timeout: {
    commandTimeout: 5000,
    responseTimeout: 10000,
    requestTimeout: 20000
  },

  // URL Configurations
  pages: [
    {
      name: 'login-page',
      url: 'https://example.com/login'
    },
    {
      name: 'dashboard-page',
      url: 'https://example.com/dashboard'
    }
  ],

  // Test Data Paths
  testDataPaths: {
    username: path.join(__dirname, 'test-data', 'username.txt'),
    password: path.join(__dirname, 'test-data', 'password.txt')
  },

  // Logging Configuration
  logging: {
    logFile: './logs/playwright.log',
    logLevel: 'info'
  }
};